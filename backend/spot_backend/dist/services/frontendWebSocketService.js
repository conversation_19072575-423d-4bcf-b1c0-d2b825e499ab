import { Server as SocketIOServer } from 'socket.io';
import { logger } from '../utils/logger.js';
import { mobulaWebSocketService } from './mobulaWebSocketService.js';
import { userActivityService } from './userActivityService.js';
class FrontendWebSocketService {
    io = null;
    connectedClients = new Map();
    pulseRoomClients = new Set();
    tradeRoomClients = new Map(); // poolAddress -> Set of clientIds
    heartbeatInterval = null;
    connectionPool = new Map();
    maxPoolSize = 100;
    poolCleanupInterval = 300000; // 5 minutes
    pulseDataInterval = null; // Track pulse data polling timer
    timers = new Set(); // Track all timers for cleanup
    initialized = false;
    tradeWebSocketService = null; // Lazy loaded
    /**
     * <PERSON>zy load the trade WebSocket service to avoid startup issues
     */
    async getTradeWebSocketService() {
        if (!this.tradeWebSocketService) {
            try {
                const { mobulaTradeWebSocketService } = await import('./mobulaTradeWebSocketService.js');
                this.tradeWebSocketService = mobulaTradeWebSocketService;
            }
            catch (error) {
                logger.error('❌ Failed to load trade WebSocket service:', error);
                return null;
            }
        }
        return this.tradeWebSocketService;
    }
    /**
     * Initialize the WebSocket server
     */
    initialize(httpServer) {
        if (this.initialized) {
            logger.warn('Frontend WebSocket service already initialized');
            return;
        }
        this.io = new SocketIOServer(httpServer, {
            cors: {
                origin: [
                    "http://localhost:4001",
                    "http://127.0.0.1:4001",
                    "https://redfyn.crypfi.io",
                    "https://redfyn.lrbinfotech.com"
                ],
                methods: ["GET", "POST"],
                credentials: false
            },
            transports: ['websocket', 'polling'],
            pingTimeout: 60000,
            pingInterval: 25000,
            allowEIO3: true
        });
        this.setupEventHandlers();
        this.startHeartbeat();
        this.setupMobulaDataListener();
        // Setup trade data listener asynchronously to avoid blocking startup
        this.setupTradeDataListener().catch(error => {
            logger.error('❌ Failed to setup trade data listener:', error);
        });
        this.initialized = true;
        logger.info('✅ Frontend WebSocket server initialized successfully');
    }
    /**
     * Setup Socket.IO event handlers
     */
    setupEventHandlers() {
        if (!this.io)
            return;
        this.io.on('connection', (socket) => {
            logger.info(`🔌 Frontend client connected: ${socket.id}`);
            // Handle client registration
            socket.on('register', (data) => {
                this.handleClientRegistration(socket, data);
            });
            // Handle pulse page join
            socket.on('join-pulse', (data) => {
                this.handlePulsePageJoin(socket, data);
            });
            // Handle pulse page leave
            socket.on('leave-pulse', (data) => {
                this.handlePulsePageLeave(socket, data);
            });
            // Handle trade data subscription
            socket.on('subscribe-trade', (data) => {
                this.handleTradeSubscription(socket, data);
            });
            // Handle trade data unsubscription
            socket.on('unsubscribe-trade', (data) => {
                this.handleTradeUnsubscription(socket, data);
            });
            // Handle heartbeat
            socket.on('heartbeat', (data) => {
                this.handleHeartbeat(socket, data);
            });
            // Handle disconnection
            socket.on('disconnect', (reason) => {
                this.handleClientDisconnection(socket, reason);
            });
            // Handle errors
            socket.on('error', (error) => {
                logger.error(`❌ Socket error for client ${socket.id}:`, error);
            });
        });
    }
    /**
     * Handle client registration
     */
    handleClientRegistration(socket, data) {
        const { userId, sessionId } = data;
        if (!userId || !sessionId) {
            socket.emit('error', { message: 'userId and sessionId are required' });
            return;
        }
        const client = {
            id: socket.id,
            userId,
            sessionId,
            connectedAt: Date.now(),
            lastActivity: Date.now(),
            isOnPulsePage: false
        };
        this.connectedClients.set(socket.id, client);
        // Send current connection status
        socket.emit('connection-status', {
            connected: true,
            clientId: socket.id,
            timestamp: Date.now()
        });
        logger.info(`📝 Client registered: ${socket.id} (User: ${userId})`);
    }
    /**
     * Handle pulse page join
     */
    handlePulsePageJoin(socket, data) {
        const { userId, sessionId } = data;
        const client = this.connectedClients.get(socket.id);
        if (!client) {
            socket.emit('error', { message: 'Client not registered' });
            return;
        }
        // Join pulse room
        socket.join('pulse-room');
        this.pulseRoomClients.add(socket.id);
        client.isOnPulsePage = true;
        client.lastActivity = Date.now();
        // Register with user activity service
        userActivityService.registerPulseActivity(userId, sessionId);
        // Send current pulse data with improved fallback handling
        const cachedData = mobulaWebSocketService.getCachedData();
        const hasFreshData = mobulaWebSocketService.hasFreshData();
        const hasUsableData = mobulaWebSocketService.hasUsableData();
        if (hasUsableData) {
            // Determine data source and freshness
            let dataSource = 'api';
            let dataAge = 0;
            if (hasFreshData) {
                dataSource = 'websocket';
            }
            else if (cachedData) {
                dataSource = 'fallback';
                // Calculate approximate age for logging
                const status = mobulaWebSocketService.getStatus();
                if (status.lastUpdate) {
                    dataAge = Date.now() - status.lastUpdate;
                }
            }
            const pulseUpdate = {
                data: cachedData,
                timestamp: Date.now(),
                source: dataSource
            };
            socket.emit('pulse-data', pulseUpdate);
            if (dataSource === 'fallback') {
                logger.info(`📤 Sent fallback pulse data to client ${socket.id} (age: ${Math.round(dataAge / 1000)}s) - fresh data will follow`);
            }
            else {
                logger.info(`📤 Sent initial pulse data to client ${socket.id} (source: ${dataSource}, fresh: ${hasFreshData})`);
            }
        }
        else {
            // No data available at all - send empty structure
            const emptyPulseUpdate = {
                data: { new: [], bonding: [], bonded: [] },
                timestamp: Date.now(),
                source: 'api'
            };
            socket.emit('pulse-data', emptyPulseUpdate);
            logger.info(`📤 Sent empty pulse data to client ${socket.id} - no cached data available`);
        }
        // Immediately ensure we have fresh data coming by checking connection status
        logger.info(`🎯 Client joined pulse room: ${socket.id} (User: ${userId}) - ensuring fresh data flow`);
        this.ensureFreshDataFlow();
        // Also trigger an immediate check for new data
        setTimeout(() => {
            this.checkForNewPulseData();
        }, 1000); // Check after 1 second to allow connection to establish
        // Emit room stats to all pulse clients
        this.broadcastPulseRoomStats();
    }
    /**
     * Handle pulse page leave
     */
    handlePulsePageLeave(socket, data) {
        const { userId, sessionId } = data;
        const client = this.connectedClients.get(socket.id);
        if (!client)
            return;
        // Leave pulse room
        socket.leave('pulse-room');
        this.pulseRoomClients.delete(socket.id);
        client.isOnPulsePage = false;
        client.lastActivity = Date.now();
        // Unregister from user activity service
        userActivityService.unregisterPulseActivity(userId, sessionId);
        logger.info(`🚪 Client left pulse room: ${socket.id} (User: ${userId})`);
        // Restore normal polling frequency if no clients remain
        if (this.pulseRoomClients.size === 0 && this.pulseDataInterval) {
            clearInterval(this.pulseDataInterval);
            this.timers.delete(this.pulseDataInterval);
            this.pulseDataInterval = setInterval(() => {
                this.checkForNewPulseData();
            }, 15000); // Back to 15 seconds when no clients
            this.timers.add(this.pulseDataInterval);
            logger.debug('🔄 Restored normal polling frequency - no active clients');
        }
        // Emit room stats to remaining pulse clients
        this.broadcastPulseRoomStats();
    }
    /**
     * Handle trade data subscription
     */
    async handleTradeSubscription(socket, data) {
        const { poolAddress, userId, sessionId } = data;
        const client = this.connectedClients.get(socket.id);
        if (!client) {
            socket.emit('error', { message: 'Client not registered' });
            return;
        }
        if (!poolAddress) {
            socket.emit('error', { message: 'Pool address is required' });
            return;
        }
        try {
            // Get trade WebSocket service
            const tradeService = await this.getTradeWebSocketService();
            if (!tradeService) {
                socket.emit('error', { message: 'Trade service not available' });
                return;
            }
            // Add client to trade room for this pool
            const roomName = `trade-${poolAddress}`;
            socket.join(roomName);
            // Track trade room clients
            if (!this.tradeRoomClients.has(poolAddress)) {
                this.tradeRoomClients.set(poolAddress, new Set());
            }
            this.tradeRoomClients.get(poolAddress).add(socket.id);
            // Subscribe to trade data from Mobula
            tradeService.subscribeToPool(poolAddress, socket.id);
            // Send existing trade history if available
            const tradeHistory = tradeService.getTradeHistory(poolAddress);
            if (tradeHistory.length > 0) {
                const tradeUpdate = {
                    poolAddress,
                    tradeData: tradeHistory[0], // Most recent trade
                    tradeHistory: tradeHistory.slice(0, 20), // Last 20 trades
                    timestamp: Date.now()
                };
                socket.emit('trade-data', tradeUpdate);
            }
            client.lastActivity = Date.now();
            logger.info(`📊 Client ${socket.id} subscribed to trade data for pool: ${poolAddress}`);
        }
        catch (error) {
            logger.error(`❌ Error handling trade subscription for ${socket.id}:`, error);
            socket.emit('error', { message: 'Failed to subscribe to trade data' });
        }
    }
    /**
     * Handle trade data unsubscription
     */
    async handleTradeUnsubscription(socket, data) {
        const { poolAddress, userId, sessionId } = data;
        const client = this.connectedClients.get(socket.id);
        if (!client)
            return;
        if (!poolAddress) {
            socket.emit('error', { message: 'Pool address is required' });
            return;
        }
        try {
            // Remove client from trade room
            const roomName = `trade-${poolAddress}`;
            socket.leave(roomName);
            // Update trade room clients tracking
            const poolClients = this.tradeRoomClients.get(poolAddress);
            if (poolClients) {
                poolClients.delete(socket.id);
                if (poolClients.size === 0) {
                    this.tradeRoomClients.delete(poolAddress);
                }
            }
            // Unsubscribe from trade data from Mobula
            const tradeService = await this.getTradeWebSocketService();
            if (tradeService) {
                tradeService.unsubscribeFromPool(poolAddress, socket.id);
            }
            client.lastActivity = Date.now();
            logger.info(`📊 Client ${socket.id} unsubscribed from trade data for pool: ${poolAddress}`);
        }
        catch (error) {
            logger.error(`❌ Error handling trade unsubscription for ${socket.id}:`, error);
        }
    }
    /**
     * Handle client heartbeat
     */
    handleHeartbeat(socket, data) {
        const client = this.connectedClients.get(socket.id);
        if (client) {
            client.lastActivity = Date.now();
            userActivityService.updateActivity(data.userId, data.sessionId);
            socket.emit('heartbeat-ack', { timestamp: Date.now() });
            logger.debug(`💓 Heartbeat received from client: ${socket.id}`);
        }
    }
    /**
     * Handle client disconnection
     */
    async handleClientDisconnection(socket, reason) {
        const client = this.connectedClients.get(socket.id);
        if (client) {
            // Clean up pulse room if client was in it
            if (client.isOnPulsePage) {
                this.pulseRoomClients.delete(socket.id);
                userActivityService.unregisterPulseActivity(client.userId, client.sessionId);
            }
            // Clean up trade room subscriptions
            try {
                const tradeService = await this.getTradeWebSocketService();
                for (const [poolAddress, clientIds] of this.tradeRoomClients.entries()) {
                    if (clientIds.has(socket.id)) {
                        clientIds.delete(socket.id);
                        if (tradeService) {
                            tradeService.unsubscribeFromPool(poolAddress, socket.id);
                        }
                        if (clientIds.size === 0) {
                            this.tradeRoomClients.delete(poolAddress);
                        }
                    }
                }
            }
            catch (error) {
                logger.error(`❌ Error cleaning up trade subscriptions for ${socket.id}:`, error);
            }
            this.connectedClients.delete(socket.id);
            logger.info(`🔌 Client disconnected: ${socket.id} (User: ${client.userId}, Reason: ${reason})`);
            // Emit room stats to remaining pulse clients
            this.broadcastPulseRoomStats();
        }
    }
    /**
     * Setup listener for Mobula WebSocket data updates
     */
    setupMobulaDataListener() {
        // We'll extend the MobulaWebSocketService to emit events when new data arrives
        // For now, we'll poll for new data periodically with reduced frequency
        this.pulseDataInterval = setInterval(() => {
            this.checkForNewPulseData();
        }, 15000); // Check every 15 seconds (reduced from 5 seconds)
        // Track timer for cleanup
        this.timers.add(this.pulseDataInterval);
    }
    /**
     * Setup listener for Mobula Trade WebSocket data updates
     */
    async setupTradeDataListener() {
        try {
            // Get trade WebSocket service
            const tradeService = await this.getTradeWebSocketService();
            if (!tradeService) {
                logger.warn('⚠️ Trade WebSocket service not available - trade functionality disabled');
                return;
            }
            // Listen for trade data events from the trade WebSocket service
            tradeService.on('tradeData', (data) => {
                this.broadcastTradeData(data.poolAddress, data.tradeData, data.tradeHistory);
            });
            tradeService.on('connected', () => {
                logger.info('📊 Mobula Trade WebSocket connected - ready to receive trade data');
            });
            tradeService.on('disconnected', () => {
                logger.warn('📊 Mobula Trade WebSocket disconnected');
            });
            tradeService.on('error', (error) => {
                logger.error('📊 Mobula Trade WebSocket error:', error);
            });
            logger.info('✅ Trade data listener setup completed');
        }
        catch (error) {
            logger.error('❌ Failed to setup trade data listener:', error);
        }
    }
    /**
     * Check for new pulse data and broadcast to clients
     */
    checkForNewPulseData() {
        if (this.pulseRoomClients.size === 0) {
            return; // No clients in pulse room
        }
        const cachedData = mobulaWebSocketService.getCachedData();
        if (cachedData && mobulaWebSocketService.hasFreshData()) {
            const pulseUpdate = {
                data: cachedData,
                timestamp: Date.now(),
                source: 'websocket'
            };
            this.broadcastToPulseRoom('pulse-data', pulseUpdate);
            logger.debug(`📡 Broadcasted pulse data to ${this.pulseRoomClients.size} clients`);
        }
        else if (this.pulseRoomClients.size > 0) {
            // We have clients but no fresh data - ensure connection is active
            this.ensureFreshDataFlow();
        }
    }
    /**
     * Ensure fresh data flow is active when clients are connected
     */
    ensureFreshDataFlow() {
        const status = mobulaWebSocketService.getStatus();
        const clientCount = this.pulseRoomClients.size;
        logger.info(`🔄 Ensuring fresh data flow: ${clientCount} clients, Mobula connected: ${status.connected}`);
        if (clientCount > 0) {
            if (!status.connected) {
                logger.info('🔄 Clients waiting for data but Mobula WebSocket not connected - triggering immediate reconnection');
                // Force reconnection by registering a temporary user
                const tempUserId = `frontend-service-${Date.now()}`;
                mobulaWebSocketService.registerUser(tempUserId);
                // Remove the temporary user after a short delay to allow connection
                setTimeout(() => {
                    mobulaWebSocketService.unregisterUser(tempUserId);
                    logger.info('🔄 Removed temporary user after connection attempt');
                }, 5000);
            }
            else {
                logger.info('🔄 Mobula WebSocket already connected, checking data freshness');
                // Even if connected, check if we have recent data
                const lastUpdate = status.lastUpdate;
                const dataAge = lastUpdate ? Date.now() - lastUpdate : Infinity;
                if (dataAge > 60000) { // If data is older than 1 minute
                    logger.info(`🔄 Data is stale (${Math.round(dataAge / 1000)}s old), requesting fresh data`);
                    // Request fresh data by briefly registering a user
                    const refreshUserId = `refresh-${Date.now()}`;
                    mobulaWebSocketService.registerUser(refreshUserId);
                    setTimeout(() => {
                        mobulaWebSocketService.unregisterUser(refreshUserId);
                    }, 2000);
                }
            }
            // Increase polling frequency when clients are active
            if (this.pulseDataInterval) {
                clearInterval(this.pulseDataInterval);
                this.timers.delete(this.pulseDataInterval);
            }
            this.pulseDataInterval = setInterval(() => {
                this.checkForNewPulseData();
            }, 5000); // Check every 5 seconds when clients are active
            this.timers.add(this.pulseDataInterval);
            logger.debug('🔄 Set active polling frequency (5s) for pulse data');
        }
    }
    /**
     * Broadcast pulse room statistics
     */
    broadcastPulseRoomStats() {
        const stats = {
            totalClients: this.connectedClients.size,
            pulseRoomClients: this.pulseRoomClients.size,
            timestamp: Date.now()
        };
        this.broadcastToPulseRoom('room-stats', stats);
    }
    /**
     * Broadcast message to all clients in pulse room
     */
    broadcastToPulseRoom(event, data) {
        if (!this.io)
            return;
        this.io.to('pulse-room').emit(event, data);
    }
    /**
     * Start heartbeat monitoring for connected clients
     */
    startHeartbeat() {
        this.heartbeatInterval = setInterval(() => {
            this.cleanupInactiveClients();
        }, 30000); // Check every 30 seconds
        // Track timer for cleanup
        this.timers.add(this.heartbeatInterval);
        logger.info('💓 Started frontend client heartbeat monitoring');
    }
    /**
     * Clean up inactive clients
     */
    cleanupInactiveClients() {
        const now = Date.now();
        const inactiveThreshold = 5 * 60 * 1000; // 5 minutes
        const clientsToRemove = [];
        for (const [socketId, client] of this.connectedClients.entries()) {
            if (now - client.lastActivity > inactiveThreshold) {
                clientsToRemove.push(socketId);
            }
        }
        clientsToRemove.forEach(socketId => {
            const client = this.connectedClients.get(socketId);
            if (client) {
                logger.info(`🧹 Cleaning up inactive client: ${socketId} (User: ${client.userId})`);
                if (client.isOnPulsePage) {
                    this.pulseRoomClients.delete(socketId);
                    userActivityService.unregisterPulseActivity(client.userId, client.sessionId);
                }
                this.connectedClients.delete(socketId);
                // Disconnect the socket if still connected
                const socket = this.io?.sockets.sockets.get(socketId);
                if (socket) {
                    socket.disconnect(true);
                }
            }
        });
        if (clientsToRemove.length > 0) {
            this.broadcastPulseRoomStats();
        }
    }
    /**
     * Manually broadcast new pulse data (called by MobulaWebSocketService)
     */
    broadcastPulseData(data, source = 'websocket') {
        if (this.pulseRoomClients.size === 0) {
            return; // No clients to broadcast to
        }
        const pulseUpdate = {
            data,
            timestamp: Date.now(),
            source
        };
        this.broadcastToPulseRoom('pulse-data', pulseUpdate);
        logger.info(`📡 Broadcasted new pulse data to ${this.pulseRoomClients.size} clients (source: ${source})`);
    }
    /**
     * Broadcast trade data to clients subscribed to a specific pool
     */
    broadcastTradeData(poolAddress, tradeData, tradeHistory) {
        const poolClients = this.tradeRoomClients.get(poolAddress);
        if (!poolClients || poolClients.size === 0) {
            return; // No clients subscribed to this pool
        }
        const tradeUpdate = {
            poolAddress,
            tradeData,
            tradeHistory: tradeHistory.slice(0, 20), // Send last 20 trades
            timestamp: Date.now()
        };
        const roomName = `trade-${poolAddress}`;
        if (this.io) {
            this.io.to(roomName).emit('trade-data', tradeUpdate);
        }
        logger.info(`📊 Broadcasted trade data for pool ${poolAddress} to ${poolClients.size} clients`);
    }
    /**
     * Check if the service is initialized
     */
    isInitialized() {
        return this.initialized;
    }
    /**
     * Get service status for health checks
     */
    getStatus() {
        return {
            initialized: this.initialized,
            totalClients: this.connectedClients.size,
            pulseRoomClients: this.pulseRoomClients.size,
            hasActiveConnections: this.connectedClients.size > 0,
            timestamp: Date.now()
        };
    }
    /**
     * Get service statistics
     */
    getStats() {
        return {
            totalClients: this.connectedClients.size,
            pulseRoomClients: this.pulseRoomClients.size,
            isInitialized: this.initialized,
            connectedClients: Array.from(this.connectedClients.values()).map(client => ({
                id: client.id,
                userId: client.userId,
                connectedAt: client.connectedAt,
                lastActivity: client.lastActivity,
                isOnPulsePage: client.isOnPulsePage
            }))
        };
    }
    /**
     * Shutdown the service gracefully with comprehensive timer cleanup
     */
    shutdown() {
        logger.info('🛑 Shutting down Frontend WebSocket service...');
        // Clear all tracked timers
        for (const timer of this.timers) {
            clearInterval(timer);
        }
        this.timers.clear();
        // Clear specific timers
        if (this.heartbeatInterval) {
            clearInterval(this.heartbeatInterval);
            this.heartbeatInterval = null;
        }
        if (this.pulseDataInterval) {
            clearInterval(this.pulseDataInterval);
            this.pulseDataInterval = null;
        }
        if (this.io) {
            this.io.close();
            this.io = null;
        }
        this.connectedClients.clear();
        this.pulseRoomClients.clear();
        this.initialized = false;
        logger.info('✅ Frontend WebSocket service shutdown complete with timer cleanup');
    }
}
// Export singleton instance
export const frontendWebSocketService = new FrontendWebSocketService();
//# sourceMappingURL=frontendWebSocketService.js.map