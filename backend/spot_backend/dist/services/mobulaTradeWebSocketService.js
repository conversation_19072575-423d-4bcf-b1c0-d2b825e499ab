import WebSocket from 'ws';
import { EventEmitter } from 'events';
import { logger } from '../utils/logger.js';
/**
 * Service for managing Mobula Trade Feed WebSocket connections
 * Handles real-time trade data for specific pool addresses
 */
class MobulaTradeWebSocketService extends EventEmitter {
    ws = null;
    subscriptions = new Map();
    reconnectAttempts = 0;
    maxReconnectAttempts = 10;
    reconnectDelay = 5000;
    maxReconnectDelay = 300000; // 5 minutes
    isConnecting = false;
    heartbeatInterval = null;
    apiKey;
    primaryWsUrl = 'wss://api.mobula.io';
    fallbackWsUrl = 'wss://api-prod.mobula.io';
    currentWsUrl;
    constructor() {
        super();
        this.apiKey = process.env.MOBULA_API_KEY || '';
        this.currentWsUrl = this.primaryWsUrl;
        if (!this.apiKey) {
            logger.error('❌ MOBULA_API_KEY not found in environment variables');
            throw new Error('MOBULA_API_KEY is required for trade feed service');
        }
        logger.info('🔧 Mobula Trade WebSocket Service initialized');
    }
    /**
     * Initialize the service
     */
    async initialize() {
        try {
            logger.info('🚀 Initializing Mobula Trade WebSocket Service...');
            await this.connect();
            logger.info('✅ Mobula Trade WebSocket Service initialized successfully');
        }
        catch (error) {
            logger.error('❌ Failed to initialize Mobula Trade WebSocket Service:', error);
            throw error;
        }
    }
    /**
     * Connect to Mobula WebSocket
     */
    async connect() {
        if (this.isConnecting || (this.ws && this.ws.readyState === WebSocket.OPEN)) {
            logger.debug('🔄 Already connecting or connected to Mobula Trade WebSocket');
            return;
        }
        this.isConnecting = true;
        logger.info(`🔌 Connecting to Mobula Trade WebSocket: ${this.currentWsUrl}`);
        try {
            this.ws = new WebSocket(this.currentWsUrl);
            this.setupWebSocketHandlers();
            // Wait for connection to open
            await new Promise((resolve, reject) => {
                const timeout = setTimeout(() => {
                    reject(new Error('Connection timeout'));
                }, 10000);
                this.ws.once('open', () => {
                    clearTimeout(timeout);
                    resolve();
                });
                this.ws.once('error', (error) => {
                    clearTimeout(timeout);
                    reject(error);
                });
            });
            this.isConnecting = false;
            this.reconnectAttempts = 0;
            this.startHeartbeat();
            logger.info('✅ Connected to Mobula Trade WebSocket successfully');
            // Resubscribe to existing subscriptions
            this.resubscribeAll();
        }
        catch (error) {
            this.isConnecting = false;
            logger.error('❌ Failed to connect to Mobula Trade WebSocket:', error);
            this.handleReconnect();
            throw error;
        }
    }
    /**
     * Setup WebSocket event handlers
     */
    setupWebSocketHandlers() {
        if (!this.ws)
            return;
        this.ws.on('open', () => {
            logger.info('🔗 Mobula Trade WebSocket connection opened');
            this.emit('connected');
        });
        this.ws.on('message', (data) => {
            try {
                const message = JSON.parse(data.toString());
                this.handleTradeMessage(message);
            }
            catch (error) {
                logger.error('❌ Failed to parse trade WebSocket message:', error);
            }
        });
        this.ws.on('close', (code, reason) => {
            logger.warn(`🔌 Mobula Trade WebSocket closed: ${code} - ${reason.toString()}`);
            this.stopHeartbeat();
            this.emit('disconnected');
            if (code !== 1000) { // Not a normal closure
                this.handleReconnect();
            }
        });
        this.ws.on('error', (error) => {
            logger.error('❌ Mobula Trade WebSocket error:', error);
            this.emit('error', error);
        });
        this.ws.on('pong', () => {
            logger.debug('🏓 Received pong from Mobula Trade WebSocket');
        });
    }
    /**
     * Handle incoming trade messages
     */
    handleTradeMessage(message) {
        try {
            // Handle different message types
            if (message.event && message.data) {
                // Error message format
                logger.warn('⚠️ Received error message from Mobula:', message);
                return;
            }
            // Check if this is trade data
            if (message.pair && message.type && message.pairData) {
                const tradeData = message;
                const poolAddress = tradeData.pair;
                // Find subscription for this pool address
                const subscription = this.subscriptions.get(poolAddress);
                if (subscription) {
                    // Add to trade history (keep last 100 trades)
                    subscription.tradeHistory.unshift(tradeData);
                    if (subscription.tradeHistory.length > 100) {
                        subscription.tradeHistory = subscription.tradeHistory.slice(0, 100);
                    }
                    subscription.lastUpdate = Date.now();
                    // Emit trade data to subscribers
                    this.emit('tradeData', {
                        poolAddress,
                        tradeData,
                        tradeHistory: subscription.tradeHistory
                    });
                    logger.debug(`📊 Received trade data for pool ${poolAddress}: ${tradeData.type} ${tradeData.token_amount_usd.toFixed(2)} USD`);
                }
            }
        }
        catch (error) {
            logger.error('❌ Error handling trade message:', error);
        }
    }
    /**
     * Subscribe to trade data for a specific pool address
     */
    subscribeToPool(poolAddress, clientId) {
        if (!poolAddress || !clientId) {
            logger.warn('⚠️ Invalid pool address or client ID for subscription');
            return;
        }
        let subscription = this.subscriptions.get(poolAddress);
        if (!subscription) {
            subscription = {
                poolAddress,
                clientIds: new Set(),
                tradeHistory: []
            };
            this.subscriptions.set(poolAddress, subscription);
        }
        subscription.clientIds.add(clientId);
        // Send subscription to Mobula if WebSocket is connected
        if (this.ws && this.ws.readyState === WebSocket.OPEN) {
            this.sendSubscription(poolAddress);
        }
        logger.info(`📡 Client ${clientId} subscribed to pool ${poolAddress} (${subscription.clientIds.size} total clients)`);
    }
    /**
     * Unsubscribe from trade data for a specific pool address
     */
    unsubscribeFromPool(poolAddress, clientId) {
        const subscription = this.subscriptions.get(poolAddress);
        if (!subscription)
            return;
        subscription.clientIds.delete(clientId);
        if (subscription.clientIds.size === 0) {
            // No more clients for this pool, remove subscription
            this.subscriptions.delete(poolAddress);
            // Send unsubscription to Mobula if WebSocket is connected
            if (this.ws && this.ws.readyState === WebSocket.OPEN) {
                this.sendUnsubscription(poolAddress);
            }
            logger.info(`📡 Removed subscription for pool ${poolAddress} (no more clients)`);
        }
        else {
            logger.info(`📡 Client ${clientId} unsubscribed from pool ${poolAddress} (${subscription.clientIds.size} remaining clients)`);
        }
    }
    /**
     * Send subscription message to Mobula
     */
    sendSubscription(poolAddress) {
        if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
            logger.warn('⚠️ Cannot send subscription - WebSocket not connected');
            return;
        }
        const subscriptionMessage = {
            type: 'pair',
            authorization: this.apiKey,
            payload: {
                blockchain: 'solana',
                address: poolAddress
            }
        };
        try {
            this.ws.send(JSON.stringify(subscriptionMessage));
            logger.info(`📤 Sent subscription for pool: ${poolAddress}`);
        }
        catch (error) {
            logger.error('❌ Failed to send subscription:', error);
        }
    }
    /**
     * Send unsubscription message to Mobula
     */
    sendUnsubscription(poolAddress) {
        if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
            return;
        }
        // Note: Mobula might not support explicit unsubscription
        // The connection will naturally stop receiving data when we disconnect
        logger.debug(`📤 Pool ${poolAddress} unsubscribed (implicit)`);
    }
    /**
     * Resubscribe to all active subscriptions
     */
    resubscribeAll() {
        for (const [poolAddress] of this.subscriptions) {
            this.sendSubscription(poolAddress);
        }
        logger.info(`🔄 Resubscribed to ${this.subscriptions.size} pools`);
    }
    /**
     * Start heartbeat to keep connection alive
     */
    startHeartbeat() {
        this.stopHeartbeat();
        this.heartbeatInterval = setInterval(() => {
            if (this.ws && this.ws.readyState === WebSocket.OPEN) {
                this.ws.ping();
                logger.debug('🏓 Sent ping to Mobula Trade WebSocket');
            }
        }, 30000); // Every 30 seconds
    }
    /**
     * Stop heartbeat
     */
    stopHeartbeat() {
        if (this.heartbeatInterval) {
            clearInterval(this.heartbeatInterval);
            this.heartbeatInterval = null;
        }
    }
    /**
     * Handle reconnection logic
     */
    handleReconnect() {
        if (this.reconnectAttempts >= this.maxReconnectAttempts) {
            logger.error('❌ Max reconnection attempts reached for Mobula Trade WebSocket');
            return;
        }
        this.reconnectAttempts++;
        const delay = Math.min(this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1), this.maxReconnectDelay);
        logger.info(`🔄 Attempting to reconnect to Mobula Trade WebSocket (${this.reconnectAttempts}/${this.maxReconnectAttempts}) in ${delay}ms`);
        setTimeout(() => {
            // Try fallback URL if primary failed
            if (this.reconnectAttempts > 2 && this.currentWsUrl === this.primaryWsUrl) {
                this.currentWsUrl = this.fallbackWsUrl;
                logger.info('🔄 Switching to fallback WebSocket URL');
            }
            this.connect().catch((error) => {
                logger.error('❌ Reconnection attempt failed:', error);
            });
        }, delay);
    }
    /**
     * Get trade history for a pool
     */
    getTradeHistory(poolAddress) {
        const subscription = this.subscriptions.get(poolAddress);
        return subscription ? subscription.tradeHistory : [];
    }
    /**
     * Get service status
     */
    getStatus() {
        return {
            connected: this.ws?.readyState === WebSocket.OPEN,
            subscriptions: this.subscriptions.size,
            reconnectAttempts: this.reconnectAttempts,
            currentUrl: this.currentWsUrl
        };
    }
    /**
     * Shutdown the service
     */
    shutdown() {
        logger.info('🛑 Shutting down Mobula Trade WebSocket Service...');
        this.stopHeartbeat();
        if (this.ws) {
            this.ws.close(1000, 'Service shutdown');
            this.ws = null;
        }
        this.subscriptions.clear();
        this.removeAllListeners();
        logger.info('✅ Mobula Trade WebSocket Service shutdown complete');
    }
}
// Export singleton instance
export const mobulaTradeWebSocketService = new MobulaTradeWebSocketService();
//# sourceMappingURL=mobulaTradeWebSocketService.js.map