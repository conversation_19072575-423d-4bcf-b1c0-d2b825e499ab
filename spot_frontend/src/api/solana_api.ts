import axios from 'axios';

// Get the PumpFun API URL from environment variables with dynamic fallback
function getPumpFunApiUrl() {
  // First try environment variable
  if (import.meta.env.VITE_PUMPFUN_API_URL) {
    console.log('Using PUMPFUN_API_URL from environment:', import.meta.env.VITE_PUMPFUN_API_URL);
    return import.meta.env.VITE_PUMPFUN_API_URL;
  }

  // Dynamic fallback based on current hostname
  const hostname = window.location.hostname;
  console.log('No PUMPFUN_API_URL found, using hostname-based fallback:', hostname);

  // Production environments
  if (hostname.includes('crypfi.io')) {
    return 'https://redfyn.crypfi.io/solana-api';
  }
  if (hostname.includes('lrbinfotech.com')) {
    return 'https://redfyn.lrbinfotech.com/solana-api';
  }

  // Development environment - use Vite proxy
  if (hostname === 'localhost' || hostname === '127.0.0.1') {
    console.log('Development environment detected, using Vite proxy');
    return '/solana-api'; // Use Vite proxy to avoid CORS issues
  }

  // Fallback for production
  return '/solana-api';
}

const PUMPFUN_API_URL = getPumpFunApiUrl();

// TypeScript interfaces for API requests and responses
export interface QuoteRequest {
  tokenAddress: string;
  poolAddress: string;
  dexType: string;
  amount: number;
  direction: 'buy' | 'sell';
  slippage: number;
}

export interface SwapRequest extends QuoteRequest {
  walletAddress: string;
  walletId?: string;
  priorityFee?: number;
  bribeAmount?: number;
  mevProtection?: boolean;
  priorityLevel?: string;
}

export interface QuoteRecommendations {
  slippage: number;
  bribeAmount: number;
  priorityFee: number;
  reasoning: string;
}

export interface QuoteResponse {
  success: boolean;
  data: {
    minOut: number; // For BUY: minimum tokens received, For SELL: minimum SOL/currency received
    recommendations: QuoteRecommendations;
    [key: string]: any; // Allow for additional fields
  };
  error?: string;
}

export interface SwapResponse {
  success: boolean;
  data: {
    signature?: string;           // Transaction signature from PumpFun
    transactionHash?: string;     // Alternative transaction hash field
    outAmount?: number;           // Amount of tokens received
    price?: number;               // Price per token
    solscanUrl?: string;          // Direct Solscan URL from PumpFun
    mevProtected?: boolean;       // MEV protection status
    tipAmount?: number;           // Tip amount paid
    executionMethod?: string;     // How the transaction was executed
    [key: string]: any;           // Allow for additional fields
  };
  error?: string;
}

export interface BalanceRequest {
  tokenAddress?: string;
  walletAddress: string;
}

export interface BalanceResponse {
  success: boolean;
  data: {
    tokenBalance: string;
    rawBalance: string;
    decimals: number;
    solBalance: string;
    tokenAccountAddress: string;
    hasTokenAccount: boolean;
  };
  error?: string;
}

/**
 * Get quote from PumpFun/PumpSwap
 */
export const pumpQuote = async (request: QuoteRequest): Promise<QuoteResponse> => {
  try {
    console.log('=== PUMPFUN API CALL DEBUG ===');
    console.log('API URL:', `${PUMPFUN_API_URL}/pump/quote`);
    console.log('Request payload:', JSON.stringify(request, null, 2));

    const response = await axios.post(`${PUMPFUN_API_URL}/pump/quote`, request, {
      headers: {
        'Content-Type': 'application/json',
      },
      timeout: 10000, // 10 second timeout
    });

    console.log('=== PUMPFUN API RESPONSE DEBUG ===');
    console.log('Status:', response.status);
    console.log('Response data:', JSON.stringify(response.data, null, 2));

    return response.data;
  } catch (error) {
    console.error('PumpFun quote error:', error);
    
    if (axios.isAxiosError(error)) {
      return {
        success: false,
        data: {
          minOut: 0,
          recommendations: {
            slippage: 0.10,
            bribeAmount: 0,
            priorityFee: 0,
            reasoning: 'Failed to get quote'
          }
        },
        error: error.response?.data?.message || error.message || 'Network error'
      };
    }

    return {
      success: false,
      data: {
        minOut: 0,
        recommendations: {
          slippage: 0.10,
          bribeAmount: 0,
          priorityFee: 0,
          reasoning: 'Unknown error occurred'
        }
      },
      error: 'Unknown error occurred'
    };
  }
};

/**
 * Execute swap on PumpFun/PumpSwap
 */
export const pumpSwap = async (request: SwapRequest): Promise<SwapResponse> => {
  try {
    const response = await axios.post(`${PUMPFUN_API_URL}/pump/swap`, request, {
      headers: {
        'Content-Type': 'application/json',
      },
      timeout: 30000, // 30 second timeout for swaps
    });

    return response.data;
  } catch (error) {
    console.error('PumpFun swap error:', error);
    
    if (axios.isAxiosError(error)) {
      return {
        success: false,
        data: {},
        error: error.response?.data?.message || error.message || 'Network error'
      };
    }

    return {
      success: false,
      data: {},
      error: 'Unknown error occurred'
    };
  }
};

/**
 * Get quote from LaunchLab
 */
export const launchlabQuote = async (request: QuoteRequest): Promise<QuoteResponse> => {
  try {
    console.log('=== LAUNCHLAB API CALL DEBUG ===');
    console.log('API URL:', `${PUMPFUN_API_URL}/launchlab/quote`);
    console.log('Request payload:', JSON.stringify(request, null, 2));

    // TODO: Replace with actual LaunchLab API endpoint when available
    const response = await axios.post(`${PUMPFUN_API_URL}/launchlab/quote`, request, {
      headers: {
        'Content-Type': 'application/json',
      },
      timeout: 10000,
    });

    console.log('=== LAUNCHLAB API RESPONSE DEBUG ===');
    console.log('Status:', response.status);
    console.log('Response data:', JSON.stringify(response.data, null, 2));

    return response.data;
  } catch (error) {
    console.error('LaunchLab quote error:', error);
    
    if (axios.isAxiosError(error)) {
      return {
        success: false,
        data: {
          minOut: 0,
          recommendations: {
            slippage: 0.10,
            bribeAmount: 0,
            priorityFee: 0,
            reasoning: 'Failed to get LaunchLab quote'
          }
        },
        error: error.response?.data?.message || error.message || 'Network error'
      };
    }

    return {
      success: false,
      data: {
        minOut: 0,
        recommendations: {
          slippage: 0.10,
          bribeAmount: 0,
          priorityFee: 0,
          reasoning: 'Unknown error occurred'
        }
      },
      error: 'Unknown error occurred'
    };
  }
};

/**
 * Execute swap on LaunchLab
 */
export const launchlabSwap = async (request: SwapRequest): Promise<SwapResponse> => {
  try {
    // TODO: Replace with actual LaunchLab API endpoint when available
    const response = await axios.post(`${PUMPFUN_API_URL}/launchlab/swap`, request, {
      headers: {
        'Content-Type': 'application/json',
      },
      timeout: 30000,
    });

    return response.data;
  } catch (error) {
    console.error('LaunchLab swap error:', error);
    
    if (axios.isAxiosError(error)) {
      return {
        success: false,
        data: {},
        error: error.response?.data?.message || error.message || 'Network error'
      };
    }

    return {
      success: false,
      data: {},
      error: 'Unknown error occurred'
    };
  }
};

/**
 * Helper function to determine which quote function to use based on exchange
 */
export const getQuoteForExchange = async (
  exchangeName: string,
  request: QuoteRequest
): Promise<QuoteResponse> => {
  console.log('=== EXCHANGE ROUTING DEBUG ===');
  console.log('Original exchange name:', exchangeName);
  console.log('Normalized exchange:', exchangeName.toLowerCase());
  console.log('Request direction:', request.direction);
  console.log('Request amount:', request.amount);

  const normalizedExchange = exchangeName.toLowerCase();

  if (normalizedExchange === 'pumpswap' || normalizedExchange === 'pumpfun') {
    console.log('Routing to PumpFun/PumpSwap API');
    return pumpQuote(request);
  } else if (normalizedExchange === 'launchlab') {
    console.log('Routing to LaunchLab API');
    return launchlabQuote(request);
  } else {
    console.log('Unsupported exchange, returning error');
    return {
      success: false,
      data: {
        minOut: 0,
        recommendations: {
          slippage: 0.10,
          bribeAmount: 0,
          priorityFee: 0,
          reasoning: `Unsupported exchange: ${exchangeName}`
        }
      },
      error: `Unsupported exchange: ${exchangeName}`
    };
  }
};

// Request cache for deduplication
interface CacheEntry {
  promise: Promise<BalanceResponse>;
  timestamp: number;
  abortController: AbortController;
}

const balanceRequestCache = new Map<string, CacheEntry>();
const CACHE_DURATION = 2000; // 2 seconds cache duration

/**
 * Get token and SOL balance for a wallet with request deduplication
 */
export const getTokenBalance = async (request: BalanceRequest): Promise<BalanceResponse> => {
  try {
    // Create cache key from request
    const cacheKey = `${request.walletAddress}-${request.tokenAddress || 'SOL'}`;

    // Check if we have a recent request in progress or cached
    const cachedEntry = balanceRequestCache.get(cacheKey);
    if (cachedEntry && (Date.now() - cachedEntry.timestamp) < CACHE_DURATION) {
      console.log('=== USING CACHED/PENDING BALANCE REQUEST ===');
      console.log('Cache key:', cacheKey);
      return await cachedEntry.promise;
    }

    console.log('=== NEW BALANCE API CALL ===');
    console.log('API URL:', `${PUMPFUN_API_URL}/token/balance`);
    console.log('Request payload:', JSON.stringify(request, null, 2));
    console.log('Cache key:', cacheKey);

    // Create AbortController for this request
    const abortController = new AbortController();

    // Create the API request promise
    const apiPromise = axios.post(`${PUMPFUN_API_URL}/token/balance`, request, {
      headers: {
        'Content-Type': 'application/json',
      },
      timeout: 10000, // 10 second timeout
      signal: abortController.signal, // Add abort signal
    }).then(response => {
      console.log('=== BALANCE API RESPONSE DEBUG ===');
      console.log('Status:', response.status);
      console.log('Response data:', JSON.stringify(response.data, null, 2));

      return response.data;
    }).catch(error => {
      console.error('Balance API error:', error);

      if (axios.isAxiosError(error)) {
        return {
          success: false,
          data: {
            tokenBalance: '0',
            rawBalance: '0',
            decimals: 0,
            solBalance: '0',
            tokenAccountAddress: '',
            hasTokenAccount: false
          },
          error: error.response?.data?.message || error.message || 'Network error'
        };
      }

      return {
        success: false,
        data: {
          tokenBalance: '0',
          rawBalance: '0',
          decimals: 0,
          solBalance: '0',
          tokenAccountAddress: '',
          hasTokenAccount: false
        },
        error: 'Unknown error occurred'
      };
    });

    // Cache the promise with abort controller
    balanceRequestCache.set(cacheKey, {
      promise: apiPromise,
      timestamp: Date.now(),
      abortController: abortController
    });

    // Clean up cache entry after completion
    apiPromise.finally(() => {
      setTimeout(() => {
        balanceRequestCache.delete(cacheKey);
      }, CACHE_DURATION);
    });

    return await apiPromise;

  } catch (error) {
    console.error('Balance API wrapper error:', error);
    return {
      success: false,
      data: {
        tokenBalance: '0',
        rawBalance: '0',
        decimals: 0,
        solBalance: '0',
        tokenAccountAddress: '',
        hasTokenAccount: false
      },
      error: 'Request failed'
    };
  }
};

/**
 * Cancel pending balance requests for a specific wallet/token combination
 */
export const cancelBalanceRequest = (walletAddress: string, tokenAddress?: string): void => {
  const cacheKey = `${walletAddress}-${tokenAddress || 'SOL'}`;
  const cachedEntry = balanceRequestCache.get(cacheKey);

  if (cachedEntry) {
    console.log('=== CANCELING BALANCE REQUEST ===');
    console.log('Cache key:', cacheKey);
    cachedEntry.abortController.abort();
    balanceRequestCache.delete(cacheKey);
  }
};

/**
 * Cancel all pending balance requests
 */
export const cancelAllBalanceRequests = (): void => {
  console.log('=== CANCELING ALL BALANCE REQUESTS ===');
  console.log('Active requests:', balanceRequestCache.size);

  balanceRequestCache.forEach((entry) => {
    entry.abortController.abort();
  });

  balanceRequestCache.clear();
};

/**
 * Helper function to determine which swap function to use based on exchange
 */
export const getSwapForExchange = async (
  exchangeName: string,
  request: SwapRequest
): Promise<SwapResponse> => {
  const normalizedExchange = exchangeName.toLowerCase();

  if (normalizedExchange === 'pumpswap' || normalizedExchange === 'pumpfun') {
    return pumpSwap(request);
  } else if (normalizedExchange === 'launchlab') {
    return launchlabSwap(request);
  } else {
    return {
      success: false,
      data: {},
      error: `Unsupported exchange: ${exchangeName}`
    };
  }
};
