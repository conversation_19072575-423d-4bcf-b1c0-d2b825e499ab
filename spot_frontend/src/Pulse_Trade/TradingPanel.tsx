import React, { useState, useEffect, useCallback, useRef } from 'react';
import { DollarSign, Wallet, Coins, CircleDollarSign } from 'lucide-react';
import SlippageSettings from './TradingPanel/SlippageSettings';
import AdvancedTradingStrategy from './TradingPanel/AdvancedTradingStrategy';
import Preset from './Preset/Preset';
import { PresetProvider } from './Preset/PresetContext';
import { getQuoteForExchange, QuoteRequest, QuoteResponse, getTokenBalance, BalanceRequest, BalanceResponse, getSwapForExchange, SwapRequest, SwapResponse, cancelBalanceRequest } from '../api/solana_api';
import { getPrivySolanaWalletInfo, preloadWalletInfo, getCachedWalletId, clearWalletCache } from '../api/privy_api';
import { showSwapSuccessToast, showSwapErrorToast, showSwapInfoToast } from '../utils/swapToast';
import { getDefaultSolanaWalletAddress, getDefaultSolanaWalletInfo } from '../utils/walletUtils';
import { PulseTokenInfo } from '../hooks/usePulseData';
import { usePrivy } from '@privy-io/react-auth';
import { useSolanaWallets } from '@privy-io/react-auth/solana';
import { usePreset } from './Preset/PresetContext';
import { useParams } from 'react-router-dom';
import TokenInfo from './TokenInfo/TokenInfo';
import { limitOrderService, CreateLimitOrderRequest } from '../services/limitOrderService';
interface BuySellToggleProps {
  isBuy: boolean;
  setIsBuy: (isBuy: boolean) => void;
}

interface TabNavigationProps {
  activeTab: string;
  setActiveTab: (tab: string) => void;
  balance: string;
  isLoadingBalance: boolean;
  isBuy: boolean;
}

interface AmountInputProps {
  amount: string;
  setAmount: (amount: string) => void;
  onQuoteUpdate?: (quote: QuoteResponse | null) => void;
  isBuy: boolean;
  balanceData?: any; // Add balance data for percentage calculations
}

interface TabProps {
  amount: string;
  setAmount: (amount: string) => void;
  isBuy: boolean;
  onQuoteUpdate?: (quote: QuoteResponse | null) => void;
  balanceData?: any; // Add balance data for percentage calculations
  onLimitOrderDataUpdate?: (data: LimitOrderData) => void; // Add limit order data callback
}

interface LimitOrderData {
  targetPrice: string;
  percentage: number;
  targetMarketCap: number;
  currentMarketCap: number;
}

interface BuyButtonProps {
  activeTab: string;
  isBuy: boolean;
  quoteData?: QuoteResponse | null;
  amount: string;
  balanceData: BalanceResponse | null; // Add balance data prop
  limitOrderData?: LimitOrderData; // Add limit order data prop
  onSwapComplete?: (result: SwapResponse) => void;
  onResetUI?: () => void; // New prop for resetting UI state
  // 🚀 BALANCE VALIDATION PROPS
  isLimitOrderBalanceValid?: boolean;
  limitOrderBalanceError?: string;
}

// Buy/Sell Toggle Component
const BuySellToggle: React.FC<BuySellToggleProps> = ({ isBuy, setIsBuy }) => (
  <div className="flex  rounded-lg p-1 m-4 ">
    <button 
      onClick={() => setIsBuy(true)}
      className={`flex-1 py-3 px-4 rounded-md font-medium transition-colors ${
        isBuy ? 'bg-[#214638] text-[#14FFA2]' : 'text-gray-400 hover:bg-[#222222] '
      }`}
    >
      Buy
    </button>
    <button 
      onClick={() => setIsBuy(false)}
      className={`flex-1 py-3 px-4 rounded-md font-medium transition-colors ${
        !isBuy ? 'bg-[#311D27] text-[#FF329B]' : 'text-gray-400 hover:bg-[#222222]'
      }`}
    >
      Sell
    </button>
  </div>
);

// Tab Navigation Component
const TabNavigation: React.FC<TabNavigationProps> = ({
  activeTab,
  setActiveTab,
  balance,
  isLoadingBalance,
  isBuy,
}) => {
  const [activePulseToken, setActivePulseToken] = useState<any | null>(null);
  const params = useParams();
  const address = params?.address;

  useEffect(() => {
    try {
      const tokenStr = localStorage.getItem('activePulseToken');
      if (tokenStr) {
        const token = JSON.parse(tokenStr);
        setActivePulseToken(token);
      }
    } catch (error) {
      console.error('Failed to parse activePulseToken:', error);
    }
  }, [address]);

  return (
    <div className="flex items-center justify-between px-4 pt-4">
      <div className="flex space-x-6">
        {['Market', 'Limit', 'Adv.'].map((tab) => (
          <button
            key={tab}
            onClick={() => setActiveTab(tab)}
            className={`pb-2 font-medium transition-colors ${
              activeTab === tab
                ? 'text-white border-b-2 border-white'
                : 'text-gray-400 hover:text-white'
            }`}
          >
            {tab}
          </button>
        ))}
      </div>
      <div className="flex items-center space-x-2 text-sm">
        <Wallet size={16} className="text-gray-400" />
        <span className="text-white">1</span>
        <div className="flex items-center space-x-1">
          {isBuy ? (
            <CircleDollarSign size={12} className="text-cyan-400" />
          ) : (
            <Coins size={12} className="text-cyan-400" />
          )}
          <span className="text-white text-xs">
            {isLoadingBalance ? (
              <div className="animate-pulse">...</div>
            ) : (
              `${balance || '0'} ${isBuy ? 'SOL' : activePulseToken?.symbol || 'TKN'}`
            )}
          </span>
        </div>
      </div>
    </div>
  );
};
// Amount Input Component
const AmountInput: React.FC<AmountInputProps> = ({ amount, setAmount, onQuoteUpdate, isBuy, balanceData }) => {
  const [inputValue, setInputValue] = useState<string>(amount);
  const [isLoadingQuote, setIsLoadingQuote] = useState<boolean>(false);

  // Debounced quote fetching
  const fetchQuote = useCallback(async (amountValue: string) => {
    if (!amountValue || parseFloat(amountValue) <= 0) {
      onQuoteUpdate?.(null);
      return;
    }

    try {
      // Get active pulse token from localStorage
      const activePulseTokenStr = localStorage.getItem('activePulseToken');
      if (!activePulseTokenStr) {
        console.log('No active pulse token found');
        return;
      }

      const activePulseToken: PulseTokenInfo = JSON.parse(activePulseTokenStr);

      // Check if token is on Solana network
      if (!activePulseToken.network?.toLowerCase().includes('solana')) {
        console.log('Token is not on Solana network:', activePulseToken.network);
        onQuoteUpdate?.(null);
        return;
      }

      // Check if exchange is supported
      const exchangeName = activePulseToken.exchange_name;
      if (!exchangeName ||
          (!['PumpSwap', 'PumpFun', 'LaunchLab'].includes(exchangeName))) {
        console.log('Unsupported exchange:', exchangeName);
        onQuoteUpdate?.(null);
        return;
      }

      setIsLoadingQuote(true);

      const quoteRequest: QuoteRequest = {
        tokenAddress: activePulseToken.address,
        poolAddress: activePulseToken.pool_address || '',
        dexType: exchangeName.toLowerCase(),
        amount: parseFloat(amountValue),
        direction: isBuy ? 'buy' : 'sell',
        slippage: 0.10 // Default slippage
      };

      const response = await getQuoteForExchange(exchangeName, quoteRequest);
      onQuoteUpdate?.(response);

    } catch (error) {
      console.error('Error fetching quote:', error);
      onQuoteUpdate?.(null);
    } finally {
      setIsLoadingQuote(false);
    }
  }, [onQuoteUpdate, isBuy]);

  // Sync inputValue with amount prop when it changes externally
  useEffect(() => {
    setInputValue(amount);
  }, [amount]);

  // Debounce quote fetching
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      fetchQuote(inputValue);
    }, 500); // 500ms debounce

    return () => clearTimeout(timeoutId);
  }, [inputValue, fetchQuote]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;

    // Only allow numbers, decimal point, and empty string
    const numericRegex = /^[0-9]*\.?[0-9]*$/;

    if (value === '' || numericRegex.test(value)) {
      // Prevent multiple decimal points
      const decimalCount = (value.match(/\./g) || []).length;
      if (decimalCount <= 1) {
        setInputValue(value);
        if (setAmount) setAmount(value);
      }
    }
  };

  const handleQuickSelect = (value: string) => {
    let calculatedAmount = value;

    // Handle percentage-based amounts for sell transactions
    if (!isBuy && balanceData?.success) {
      const tokenBalance = parseFloat(balanceData.data.tokenBalance);

      if (value.includes('%')) {
        const percentage = parseFloat(value.replace('%', ''));
        calculatedAmount = (tokenBalance * (percentage / 100)).toFixed(6);
      } else if (value === 'MAX') {
        // For sell MAX, use 99% to leave some buffer for fees
        calculatedAmount = (tokenBalance * 0.99).toFixed(6);
      }
    } else if (isBuy && balanceData?.success) {
      if (value === 'MAX') {
        // For buy MAX, use available SOL minus fee reserve
        const solBalance = parseFloat(balanceData.data.solBalance);
        const reserveAmount = 0.01; // Reserve for transaction fees
        calculatedAmount = Math.max(0, solBalance - reserveAmount).toFixed(4);
      }
      // For other buy values (0.01, 0.1, 1, 10), use the value as-is
    }

    setInputValue(calculatedAmount);
    if (setAmount) setAmount(calculatedAmount);
  };

  return (
    <div className="mb-6">
      {/* Top: Label + Input + Icon */}
      <div className="">
        <div className="relative">
          {/* Left-side 'Amount' label inside input */}
          <span className="absolute left-4 top-1/2 transform -translate-y-1/2 text-sm text-slate-400">
            Amount
          </span>
  
          {/* Right-side Solana icon */}
          <img
            src="https://metacore.mobula.io/78ee4d656f4f152a90d733f4eaaa4e1685e25bc654087acdb62bfe494d668976.png"
            alt="Solana"
            className="absolute right-3 top-1/2 transform -translate-y-1/2 h-5 w-5"
            onError={(e) => {
              (e.target as HTMLImageElement).style.display = 'none';
            }}
          />
  
          <input
            type="text"
            value={inputValue}
            onChange={handleInputChange}
            onKeyDown={(e) => {
              // Prevent 'e', 'E', '+', '-' which are valid in number inputs but not desired here
              if (['e', 'E', '+', '-'].includes(e.key)) {
                e.preventDefault();
              }
            }}
            placeholder="0.0"
            className={`w-full bg-[#2A2D35] border border-gray-700 text-white rounded-md py-2 pl-20 pr-12 text-sm focus:outline-none focus:border-gray-500 ${
              isLoadingQuote ? 'opacity-75' : ''
            }`}
          />
          {/* Dynamic currency icon */}
          <div className="absolute right-8 top-1/2 transform -translate-y-1/2">
            {isLoadingQuote ? (
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
            ) : (
              isBuy ? (
                <CircleDollarSign size={16} className="text-cyan-400" />
              ) : (
                <Coins size={16} className="text-cyan-400" />
              )
            )}
          </div>
        </div>
      </div>
  
      {/* 🚀 BALANCE DISPLAY */}
      {balanceData?.success && (
        <div className="mt-2 mb-1 flex justify-between items-center text-xs">
          <span className="text-gray-400">Available:</span>
          <span className="text-gray-300">
            {isBuy ? (
              <>
                {parseFloat(balanceData.data.solBalance).toFixed(4)} SOL
              </>
            ) : (
              <>
                {parseFloat(balanceData.data.tokenBalance) > 1
                  ? Math.round(parseFloat(balanceData.data.tokenBalance)).toString()
                  : parseFloat(balanceData.data.tokenBalance).toFixed(6)
                } tokens
              </>
            )}
          </span>
        </div>
      )}

      {/* Bottom: Quick Select Buttons */}
      <div className="grid grid-cols-5 gap-2 mt-3">
        {(isBuy
          ? ['0.01', '0.1', '1', '10', 'MAX'] // Buy: Fixed SOL amounts
          : ['10%', '25%', '50%', '75%', 'MAX'] // Sell: Percentage of token balance
        ).map((value) => (
          <button
            key={value}
            onClick={() => handleQuickSelect(value)}
            className={`border border-gray-700 hover:bg-gray-700 text-gray-300 py-2 px-3 text-sm transition-colors focus:outline-none focus:ring-2 focus:ring-gray-500 ${
              value === 'MAX' ? 'bg-gradient-to-r from-cyan-600 to-blue-600 hover:from-cyan-500 hover:to-blue-500 text-white font-medium' : ''
            }`}
          >
            {value}
          </button>
        ))}
      </div>
    </div>
  );
  
}
  
  




// Market Tab Component
const MarketTab: React.FC<TabProps> = ({ amount, setAmount, isBuy, onQuoteUpdate, balanceData }) => {
  const [showAdvancedStrategy, setShowAdvancedStrategy] = useState<boolean>(false);

  return (
    <div>
      <AmountInput amount={amount} setAmount={setAmount} isBuy={isBuy} onQuoteUpdate={onQuoteUpdate} balanceData={balanceData} />
      <SlippageSettings isBuy={isBuy} />

      <div className="mb-6">
        <label className="flex items-center space-x-2 text-gray-300 w-full">
          <input 
            type="checkbox" 
            className="rounded" 
            checked={showAdvancedStrategy}
            onChange={(e) => setShowAdvancedStrategy(e.target.checked)}
          />
          <span>Advanced Trading Strategy</span>
        </label>

        {/* Conditionally Render the Component */}
        {showAdvancedStrategy && (
          <div className="mt-4 w-full">
            <AdvancedTradingStrategy />
          </div>
        )}
      </div>
    </div>
  );
};

// Limit Tab Component
const LimitTab: React.FC<TabProps> = ({ amount, setAmount, isBuy, onQuoteUpdate, balanceData, onLimitOrderDataUpdate }) => {
  const [sliderValue, setSliderValue] = useState<number>(0);
  const [percentage, setPercentage] = useState<number>(0);
  const [activePulseToken, setActivePulseToken] = useState<any | null>(null);
  const [currentMarketCap, setCurrentMarketCap] = useState<number>(0);
  const [targetMarketCap, setTargetMarketCap] = useState<number>(0);
  const [targetPrice, setTargetPrice] = useState<string>('0.00000000');

  const params = useParams();
  const address = params?.address;

  // Load active token data and calculate initial values
  useEffect(() => {
    try {
      const tokenStr = localStorage.getItem('activePulseToken');
      if (tokenStr) {
        const token = JSON.parse(tokenStr);
        setActivePulseToken(token);

        // Set current market cap from token data
        const marketCap = token.market_cap || 0;
        setCurrentMarketCap(marketCap);
        setTargetMarketCap(marketCap); // Initialize target to current

        // Calculate initial target price
        if (marketCap > 0 && token.supply > 0) {
          const price = marketCap / token.supply;
          setTargetPrice(price.toFixed(8));
        }
      }
    } catch (error) {
      console.error('Failed to parse activePulseToken:', error);
    }
  }, [address]);

  // Update target market cap and price when slider changes
  useEffect(() => {
    if (currentMarketCap > 0) {
      // Calculate target market cap: Current MC × (1 + percentage/100)
      const newTargetMarketCap = currentMarketCap * (1 + percentage / 100);
      setTargetMarketCap(newTargetMarketCap);

      // Calculate target price: Target MC / Token Supply
      if (activePulseToken?.supply > 0) {
        const newTargetPrice = newTargetMarketCap / activePulseToken.supply;
        setTargetPrice(newTargetPrice.toFixed(8));
      }
    }
  }, [percentage, currentMarketCap, activePulseToken?.supply]);

  // Update parent component with limit order data
  useEffect(() => {
    if (onLimitOrderDataUpdate) {
      onLimitOrderDataUpdate({
        targetPrice,
        percentage,
        targetMarketCap,
        currentMarketCap
      });
    }
  }, [targetPrice, percentage, targetMarketCap, currentMarketCap, onLimitOrderDataUpdate]);

  // Format market cap for display
  const formatMarketCap = (cap: number) => {
    if (cap >= 1_000_000_000) return `${(cap / 1_000_000_000).toFixed(2)}B`;
    if (cap >= 1_000_000) return `${(cap / 1_000_000).toFixed(2)}M`;
    if (cap >= 1_000) return `${(cap / 1_000).toFixed(2)}K`;
    return cap.toFixed(0);
  };

  // Handle slider change
  const handleSliderChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseInt(e.target.value);
    setSliderValue(value);
    setPercentage(value);
  };



  return (
    <div>
      <AmountInput amount={amount} setAmount={setAmount} isBuy={isBuy} onQuoteUpdate={onQuoteUpdate} balanceData={balanceData} />
      <SlippageSettings isBuy={isBuy}/>

      <div className="mb-6">
        {/* Current vs Target Market Cap Display */}
        <div className="flex items-center justify-between mb-2">
          <span className="text-gray-400 text-sm">MKT CAP</span>
          <div className="flex items-center space-x-2">
            <span className="text-gray-400 text-sm">${formatMarketCap(currentMarketCap)}</span>
            <span className="text-gray-500">→</span>
            <span className="text-white font-medium">${formatMarketCap(targetMarketCap)}</span>
          </div>
          <DollarSign size={16} className="text-gray-400" />
        </div>

        {/* Slider */}
        <div className="relative mb-4">
          <input
            type="range"
            min="-100"
            max="100"
            value={sliderValue}
            onChange={handleSliderChange}
            className="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer slider"
          />
          <div className="flex justify-between text-xs text-gray-400 mt-1">
            <span>-100%</span>
            <span>-50%</span>
            <span>0%</span>
            <span>+50%</span>
            <span>+100%</span>
          </div>
        </div>

        <div className="flex justify-end mb-4">
          <div className="bg-gray-800 px-3 py-2 rounded flex items-center space-x-2">
            <span className={`text-white font-medium ${
              percentage > 0 ? 'text-green-400' : percentage < 0 ? 'text-red-400' : 'text-white'
            }`}>
              {percentage > 0 ? '+' : ''}{percentage.toFixed(1)}
            </span>
            <span className="text-gray-400">%</span>
          </div>
        </div>

        {/* Target Price Info Display */}
        {activePulseToken?.symbol && (
          <div className="mb-4 p-3 bg-gray-800/50 rounded-md">
            <div className="flex justify-between items-center text-xs">
              <span className="text-gray-400">Current Price:</span>
              <span className="text-white">${activePulseToken.price?.toFixed(8) || '0.00000000'}</span>
            </div>
            <div className="flex justify-between items-center text-xs mt-1">
              <span className="text-gray-400">Target Price:</span>
              <span className="text-green-400 font-medium">${targetPrice}</span>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

// Advanced Tab Component
const AdvancedTab: React.FC<TabProps> = ({ amount, setAmount, isBuy, onQuoteUpdate, balanceData }) => {
  return (
    <div>
      <AmountInput amount={amount} setAmount={setAmount} isBuy={isBuy} onQuoteUpdate={onQuoteUpdate} balanceData={balanceData} />
      <SlippageSettings isBuy={isBuy}/>
    </div>
  );
};

// Buy Button Component
const BuyButton: React.FC<BuyButtonProps> = ({
  activeTab,
  isBuy,
  quoteData,
  amount,
  balanceData,
  limitOrderData,
  onSwapComplete,
  onResetUI,
  isLimitOrderBalanceValid,
  limitOrderBalanceError
}) => {
  const [isExecuting, setIsExecuting] = useState(false);
  const [executionStage, setExecutionStage] = useState<string>('');
  const [executionProgress, setExecutionProgress] = useState<number>(0);
  const { authenticated, user } = usePrivy();
  const { wallets: solanaWallets } = useSolanaWallets();
  const preset = usePreset();
  const [activePulseToken, setActivePulseToken] = useState<any | null>(null);

  const params = useParams();
  const address = params?.address;

  useEffect(() => {
    try {
      const tokenStr = localStorage.getItem('activePulseToken');
      if (tokenStr) {
        const token = JSON.parse(tokenStr);
        setActivePulseToken(token);
      }
    } catch (error) {
      console.error('Failed to parse activePulseToken:', error);
    }
  }, [address]); // Updates when address param changes

  const formatMarketCap = (cap: number | string) => {
    if (typeof cap === 'number') {
      if (cap >= 1_000_000_000) return `${(cap / 1_000_000_000).toFixed(2)}B`;
      if (cap >= 1_000_000) return `${(cap / 1_000_000).toFixed(2)}M`;
      if (cap >= 1_000) return `${(cap / 1_000).toFixed(2)}K`;
      return cap.toString();
    }
    return cap;
  };

  // Get Solana wallet address and ID (optimized with caching)
  const getSolanaWalletInfo = async () => {
    return await getDefaultSolanaWalletInfo(authenticated, user?.id, solanaWallets);
  };

  // Execute transaction function - handles both market orders and limit orders
  const executeTransaction = async () => {
    if (!authenticated) {
      showSwapErrorToast('Please connect your wallet first');
      return;
    }

    const walletInfo = await getSolanaWalletInfo();
    if (!walletInfo) {
      // Enhanced error message with debugging info
      const errorMessage = `No Solana wallet found. Debug info:
      - Authenticated: ${authenticated}
      - Solana wallets count: ${solanaWallets.length}
      - User ID: ${user?.id || 'Not available'}
      - Wallets: ${JSON.stringify(solanaWallets.map(w => ({ address: w.address, type: w.walletClientType })), null, 2)}`;

      console.error(errorMessage);
      showSwapErrorToast('No Solana wallet found. Please ensure you have a Solana wallet connected through Privy.');
      return;
    }

    // Get active token info
    const activePulseTokenStr = localStorage.getItem('activePulseToken');
    if (!activePulseTokenStr) {
      showSwapErrorToast('No token selected');
      return;
    }

    const activePulseToken: PulseTokenInfo = JSON.parse(activePulseTokenStr);

    if (!activePulseToken.exchange_name ||
        !['PumpSwap', 'PumpFun', 'LaunchLab'].includes(activePulseToken.exchange_name)) {
      showSwapErrorToast('Unsupported exchange');
      return;
    }

    if (parseFloat(amount) <= 0) {
      showSwapErrorToast('Please enter a valid amount');
      return;
    }

    // Branch execution based on active tab
    if (activeTab === 'Limit') {
      await executeLimitOrder(walletInfo, activePulseToken);
    } else {
      await executeMarketOrder(walletInfo, activePulseToken);
    }
  };

  // Execute limit order creation
  const executeLimitOrder = async (walletInfo: any, activePulseToken: PulseTokenInfo) => {
    // Validate limit order data
    if (!limitOrderData || !limitOrderData.targetPrice || parseFloat(limitOrderData.targetPrice) <= 0) {
      showSwapErrorToast('Please set a valid target price using the slider');
      return;
    }

    if (!user?.id) {
      showSwapErrorToast('User ID not available');
      return;
    }

    // 🚀 BALANCE VALIDATION FOR LIMIT ORDERS
    const balanceValidationResult = validateLimitOrderBalance();
    if (!balanceValidationResult.isValid) {
      showSwapErrorToast(balanceValidationResult.errorMessage);
      return;
    }

    setIsExecuting(true);
    setExecutionStage('Creating limit order...');
    setExecutionProgress(20);

    try {
      // Get current preset settings for slippage
      const currentPresetSettings = preset.activePreset && preset.presetData[preset.activePreset]
        ? preset.presetData[preset.activePreset][isBuy ? 'buy' : 'sell']
        : {
            slippage: 10,
            priority: 0.0001,
            bribe: 0.00005,
            mevMode: 'Off'
          };

      setExecutionStage('Preparing limit order data...');
      setExecutionProgress(40);

      // Prepare limit order request
      const limitOrderRequest: CreateLimitOrderRequest = {
        user_id: user.id,
        token_address: activePulseToken.address,
        token_name: activePulseToken.name || 'Unknown Token',
        token_symbol: activePulseToken.symbol || 'UNKNOWN',
        token_image: activePulseToken.imageUrl || '', // Include token image
        pool_address: activePulseToken.pool_address || '',
        dex_type: activePulseToken.exchange_name.toLowerCase(),
        direction: isBuy ? 'buy' : 'sell',
        amount: parseFloat(amount),
        target_price: parseFloat(limitOrderData.targetPrice),
        current_price: activePulseToken.price || 0,
        current_market_cap: limitOrderData.currentMarketCap || activePulseToken.market_cap,
        target_market_cap: limitOrderData.targetMarketCap,
        slippage: currentPresetSettings.slippage / 100, // Convert percentage to decimal
        wallet_address: walletInfo.address,
        wallet_id: walletInfo.id,
        expires_at: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString() // 24 hours from now
      };

      console.log('=== CREATING LIMIT ORDER ===');
      console.log('Limit Order Request:', limitOrderRequest);

      setExecutionStage('Submitting limit order...');
      setExecutionProgress(70);

      const result = await limitOrderService.createLimitOrder(limitOrderRequest);

      console.log('=== LIMIT ORDER RESULT ===');
      console.log('Limit Order Response:', result);

      if (result.success && result.data) {
        setExecutionStage('Limit order created!');
        setExecutionProgress(100);

        const tokenSymbol = activePulseToken.symbol || 'TOKEN';
        const targetPrice = parseFloat(limitOrderData.targetPrice);
        const formattedPrice = targetPrice >= 0.01
          ? `$${targetPrice.toFixed(4)}`
          : `$${targetPrice.toFixed(8)}`;

        // Use info toast for limit order creation since it doesn't have a transaction signature
        showSwapInfoToast(
          `${isBuy ? 'Buy' : 'Sell'} limit order created @ ${formattedPrice} for ${tokenSymbol}`
        );

        // Reset UI state
        onResetUI?.();

        // Dispatch custom event for limit order creation
        setTimeout(() => {
          console.log('Triggering limit order creation event');
          window.dispatchEvent(new CustomEvent('limitOrderCreated', {
            detail: {
              tokenSymbol,
              direction: isBuy ? 'buy' : 'sell',
              amount: parseFloat(amount),
              targetPrice: parseFloat(limitOrderData.targetPrice),
              orderId: result.data?.id,
              timestamp: Date.now()
            }
          }));
        }, 1000);
      } else {
        setExecutionStage('Limit order failed');
        setExecutionProgress(0);
        const errorMessage = result.error || 'Failed to create limit order';
        showSwapErrorToast(errorMessage);
      }

    } catch (error) {
      console.error('Limit order creation error:', error);
      setExecutionStage('Error occurred');
      setExecutionProgress(0);
      showSwapErrorToast(error instanceof Error ? error.message : 'Unknown error occurred');
    } finally {
      // Reset execution states after a short delay to show final status
      setTimeout(() => {
        setIsExecuting(false);
        setExecutionStage('');
        setExecutionProgress(0);
      }, 2000);
    }
  };

  // Execute market order (original swap logic)
  const executeMarketOrder = async (walletInfo: any, activePulseToken: PulseTokenInfo) => {
    // Validate balance for both buy and sell transactions
    if (balanceData?.success) {
      const requestedAmount = parseFloat(amount);

      if (isBuy) {
        // Validate SOL balance for buy transactions
        const solBalance = parseFloat(balanceData.data.solBalance);

        if (solBalance < requestedAmount) {
          const shortfall = requestedAmount - solBalance;
          showSwapErrorToast(
            `Insufficient SOL balance. You have ${solBalance.toFixed(4)} SOL, ` +
            `but trying to spend ${requestedAmount} SOL. Shortfall: ${shortfall.toFixed(4)} SOL.`
          );
          return;
        }

        // Warn if spending more than 95% of SOL balance
        if (requestedAmount > solBalance * 0.95) {
          console.warn(`Spending ${((requestedAmount / solBalance) * 100).toFixed(1)}% of SOL balance`);
        }

        // Reserve some SOL for transaction fees (minimum 0.01 SOL)
        const reserveAmount = 0.01;
        if (solBalance - requestedAmount < reserveAmount) {
          showSwapErrorToast(
            `Insufficient SOL for transaction fees. Please reserve at least ${reserveAmount} SOL for fees. ` +
            `Maximum spendable: ${Math.max(0, solBalance - reserveAmount).toFixed(4)} SOL.`
          );
          return;
        }
      } else {
        // Validate token balance for sell transactions
        const tokenBalance = parseFloat(balanceData.data.tokenBalance);

        if (tokenBalance < requestedAmount) {
          const shortfall = requestedAmount - tokenBalance;
          const maxSellable = Math.floor(tokenBalance * 0.99 * 1000000) / 1000000; // 99% of balance, rounded to 6 decimals
          showSwapErrorToast(
            `Insufficient token balance. You have ${tokenBalance.toFixed(6)} tokens, ` +
            `but trying to sell ${requestedAmount} tokens. Shortfall: ${shortfall.toFixed(6)} tokens. ` +
            `Maximum sellable amount: ${maxSellable.toFixed(6)} tokens.`
          );
          return;
        }

        // Warn if selling more than 95% of balance
        if (requestedAmount > tokenBalance * 0.95) {
          console.warn(`Selling ${((requestedAmount / tokenBalance) * 100).toFixed(1)}% of token balance`);
        }
      }
    } else {
      // If balance data is not available, show a warning but allow transaction to proceed
      console.warn('Balance data not available, proceeding without balance validation');
      showSwapInfoToast('Balance data unavailable - proceeding with transaction');
    }

    setIsExecuting(true);
    setExecutionStage('Preparing transaction...');
    setExecutionProgress(10);

    try {
      // Get current preset settings
      setExecutionStage('Loading preset settings...');
      setExecutionProgress(20);

      const currentPresetSettings = preset.activePreset && preset.presetData[preset.activePreset]
        ? preset.presetData[preset.activePreset][isBuy ? 'buy' : 'sell']
        : {
            slippage: 10,
            priority: 0.0001,
            bribe: 0.00005,
            mevMode: 'Off'
          };

      // Prepare swap request with all required parameters
      setExecutionStage('Building transaction...');
      setExecutionProgress(40);

      const swapRequest: SwapRequest = {
        tokenAddress: activePulseToken.address,
        poolAddress: activePulseToken.pool_address || '',
        dexType: activePulseToken.exchange_name.toLowerCase(),
        amount: parseFloat(amount),
        direction: isBuy ? 'buy' : 'sell',
        slippage: currentPresetSettings.slippage / 100, // Convert percentage to decimal
        walletAddress: walletInfo.address,
        walletId: walletInfo.id,

        // MEV Protection settings from preset - only enable for known MEV modes
        mevProtection: currentPresetSettings.mevMode === 'Off' || currentPresetSettings.mevMode === 'Off',
        bribeAmount: currentPresetSettings.bribe, // Send as SOL, backend will convert properly
        // Enhanced priority level mapping to utilize all four levels: 'low' | 'medium' | 'high' | 'veryHigh'
        priorityLevel: currentPresetSettings.mevMode === 'Sec.' ? 'veryHigh' :   // Secure mode: maximum priority
                      currentPresetSettings.mevMode === 'Red.' ? 'high' :        // Reduced mode: high priority
                      currentPresetSettings.mevMode === 'Off' ? 'low' :       // MEV off: reliable medium priority
                      'low', // Fallback for any unknown modes
        priorityFee: currentPresetSettings.priority // Send as SOL, backend will convert properly
      };

      console.log('=== EXECUTING SWAP ===');
      console.log('Preset MEV Mode:', currentPresetSettings.mevMode);
      console.log('MEV Protection Enabled:', swapRequest.mevProtection);
      console.log('Priority Level:', swapRequest.priorityLevel);
      console.log('Bribe Amount:', swapRequest.bribeAmount, 'SOL');
      console.log('Full Swap Request:', swapRequest);

      setExecutionStage('Submitting to blockchain...');
      setExecutionProgress(60);

      const result = await getSwapForExchange(activePulseToken.exchange_name, swapRequest);

      console.log('=== SWAP RESULT ===');
      console.log('Swap Response:', result);

      if (result.success && result.data) {
        setExecutionStage('Transaction confirmed!');
        setExecutionProgress(100);

        // Extract transaction signature from response
        const transactionSignature = result.data.signature || result.data.transactionHash || '';
        const tokenSymbol = activePulseToken.symbol || 'TOKEN';

        // Show success toast with Solscan link
        if (transactionSignature) {
          showSwapSuccessToast(
            isBuy ? 'buy' : 'sell',
            tokenSymbol,
            transactionSignature,
            result.data.solscanUrl // Use provided URL if available
          );
        } else {
          showSwapInfoToast(`Successfully ${isBuy ? 'bought' : 'sold'} ${tokenSymbol}`);
        }

        // Reset UI state
        onResetUI?.();

        // Call completion callback
        onSwapComplete?.(result);

        // Dispatch custom event for balance refresh
        setTimeout(() => {
          console.log('Triggering balance refresh after successful trade');
          window.dispatchEvent(new CustomEvent('pulseTradeSuccess', {
            detail: {
              tokenSymbol,
              transactionSignature,
              direction: isBuy ? 'buy' : 'sell',
              amount: parseFloat(amount),
              timestamp: Date.now()
            }
          }));
        }, 1000); // 1 second delay for immediate feedback
      } else {
        setExecutionStage('Transaction failed');
        setExecutionProgress(0);
        // Enhanced error handling for MEV-specific errors
        const errorMessage = result.error || 'Swap failed for unknown reason';

        if (errorMessage.includes('Jito') || errorMessage.includes('bundle')) {
          showSwapErrorToast(`MEV Protection failed: ${errorMessage}. Try disabling MEV protection in settings.`);
        } else if (errorMessage.includes('bribe') || errorMessage.includes('tip')) {
          showSwapErrorToast(`MEV tip too low: ${errorMessage}. Try increasing bribe amount in settings.`);
        } else if (errorMessage.includes('timeout') && currentPresetSettings.mevMode !== 'Off') {
          showSwapErrorToast(`Transaction timeout (MEV protection may be causing delays): ${errorMessage}`);
        } else if (errorMessage.includes('slippage') || errorMessage.includes('TooLittleSolReceived')) {
          showSwapErrorToast(`Slippage protection triggered: ${errorMessage}. Try increasing slippage tolerance.`);
        } else {
          showSwapErrorToast(errorMessage);
        }
      }

    } catch (error) {
      console.error('Swap execution error:', error);
      setExecutionStage('Error occurred');
      setExecutionProgress(0);
      showSwapErrorToast(error instanceof Error ? error.message : 'Unknown error occurred');
    } finally {
      // Reset execution states after a short delay to show final status
      setTimeout(() => {
        setIsExecuting(false);
        setExecutionStage('');
        setExecutionProgress(0);
      }, 2000);
    }
  };

  const getButtonText = () => {

    if (isExecuting) {
      if (executionStage) {
        return `${executionStage} (${executionProgress}%)`;
      }
      return `${isBuy ? 'Buying' : 'Selling'}...`;
    }

    if (activeTab === 'Limit') {
      if (limitOrderData && limitOrderData.targetPrice && parseFloat(limitOrderData.targetPrice) > 0) {
        // Format target price for display
        const targetPrice = parseFloat(limitOrderData.targetPrice);
        const formattedPrice = targetPrice >= 0.01
          ? `$${targetPrice.toFixed(4)}`
          : `$${targetPrice.toFixed(8)}`;

        return `${isBuy ? 'Buy' : 'Sell'} @ ${formattedPrice}`;
      } else {
        // Fallback when no target price is set
        return `${isBuy ? 'Buy' : 'Sell'} Limit Order`;
      }
    }

    // Display minOut value if quote data is available
    if (quoteData?.success && quoteData.data?.minOut) {
      const minOut = quoteData.data.minOut;

      if (isBuy) {
        // Buy mode: Format as integer + token symbol
        const formattedAmount = Math.round(minOut).toString();

        return `Buy ${formattedAmount} ${activePulseToken?.symbol}`;
      } else {
        // Sell mode: Show raw minOut value without formatting
        return `Sell ${minOut}`;
      }
    }

    return `${isBuy ? 'Buy' : 'Sell'} ${activePulseToken?.symbol}`;
  };

  // 🚀 BALANCE VALIDATION FOR LIMIT ORDERS
  const isLimitOrderDisabled = activeTab === 'Limit' && (!isLimitOrderBalanceValid || isLimitOrderBalanceValid === false);
  const isButtonDisabled = isExecuting || !authenticated || parseFloat(amount) <= 0 || isLimitOrderDisabled;

  return (
    <div className="relative">
      {/* 🚀 BALANCE ERROR MESSAGE FOR LIMIT ORDERS */}
      {activeTab === 'Limit' && limitOrderBalanceError && (
        <div className="mb-3 p-3 bg-red-900/20 border border-red-500/30 rounded-md">
          <div className="flex items-start space-x-2">
            <div className="flex-shrink-0 mt-0.5">
              <svg className="w-4 h-4 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="text-red-300 text-sm">{limitOrderBalanceError}</div>
          </div>
        </div>
      )}

      <button
        onClick={executeTransaction}
        disabled={isButtonDisabled}
        className={`w-full py-4 rounded-full font-medium text-lg transition-colors relative overflow-hidden ${
          isButtonDisabled
            ? 'bg-gray-600 text-gray-400 cursor-not-allowed'
            : isBuy
              ? 'bg-[#214638] hover:bg-[#14ffa2] hover:text-[#214638] text-[#14FFA2]'
              : 'bg-[#311D27] hover:bg-[#FF329B] hover:text-[#311d27] text-[#FF329B]'
        }`}
      >
        {/* Progress bar background */}
        {isExecuting && (
          <div
            className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent transition-all duration-300"
            style={{
              width: `${executionProgress}%`,
              background: isBuy
                ? 'linear-gradient(90deg, transparent, rgba(20, 255, 162, 0.2), transparent)'
                : 'linear-gradient(90deg, transparent, rgba(255, 50, 155, 0.2), transparent)'
            }}
          />
        )}
        <span className="relative z-10">{getButtonText()}</span>
      </button>
    </div>
  );
};

const PortfolioStats: React.FC = () => {
  const [currency, setCurrency] = useState<string>("sol");
  const formatValue = (valueSol: string, valueUsd: string) => {
    return currency === "usd" ? (
      `$${valueUsd}`
    ) : (
      <span className="flex items-center gap-1">
        <img src={"https://metacore.mobula.io/78ee4d656f4f152a90d733f4eaaa4e1685e25bc654087acdb62bfe494d668976.png"} alt="Solana" className="w-4 h-4" />
        {valueSol}
      </span>
    );
  };

  return (
  
<div className="mt-6 text-sm bg-[#1A1D21]  overflow-hidden border border-gray-700">
      <div className="grid grid-cols-4 divide-x divide-gray-700 text-white">
        {/* Bought */}
        <div className="flex flex-col items-center justify-center px-4 py-3 hover:bg-[#22262A] transition-colors">
          <div className="text-gray-400 text-xs mb-1">Bought</div>
          <div className="flex items-center space-x-1">

            <span className="text-cyan-400">{formatValue("0", "0")}</span>
          </div>
        </div>

        {/* Sold */}
        <div className="flex flex-col items-center justify-center px-4 py-3 hover:bg-[#22262A] transition-colors">
          <div className="text-gray-400 text-xs mb-1">Sold</div>
          <div className="flex items-center space-x-1">
 
            <span className="text-pink-400">{formatValue("0", "0")}</span>
          </div>
        </div>

        {/* Holding */}
        <div className="flex flex-col items-center justify-center px-4 py-3 hover:bg-[#22262A] transition-colors">
          <div className="text-gray-400 text-xs mb-1">Holding</div>
          <div className="flex items-center space-x-1">
           
            <span className="text-cyan-400">{formatValue("0", "0.")}</span>
          </div>
        </div>

        {/* PnL with $ toggle */}
{/* PnL with $ toggle */}
<div className="flex flex-col items-center justify-center px-4 py-3 hover:bg-[#22262A] transition-colors relative">
  <div className="text-gray-400 text-xs mb-1 flex items-center gap-2">
    PnL 
    <button
      onClick={() => setCurrency(currency === "usd" ? "sol" : "usd")}
      className={`w-4 h-4 flex items-center justify-center rounded-full border text-xs transition-colors ${
        currency === "usd"
          ? "bg-green-600 border-green-400 text-white"
          : "border-gray-500 text-gray-400"
      }`}
      title="Toggle currency"
    >
      $
    </button>
  </div>

  <div className="flex items-center justify-center min-w-[100px] text-cyan-400">
    {currency === "sol" && (
      <img
        src="https://metacore.mobula.io/78ee4d656f4f152a90d733f4eaaa4e1685e25bc654087acdb62bfe494d668976.png"
        alt="Solana"
        className="w-4 h-4 mr-1"
      />
    )}
    <span>{currency === "usd" ? "+$0 (+0%)" : "+0 (+0%)"}</span>
  </div>
</div>

      </div>
    </div>
);
};

// Main Trading Interface Component
const TradingPanel = () => {
  const [activeTab, setActiveTab] = useState('Market');
  const [isBuy, setIsBuy] = useState(true);
  const [amount, setAmount] = useState('0.0');
  const [quoteData, setQuoteData] = useState<QuoteResponse | null>(null);
  const [balanceData, setBalanceData] = useState<BalanceResponse | null>(null);
  const [isLoadingBalance, setIsLoadingBalance] = useState<boolean>(false);
  const [limitOrderData, setLimitOrderData] = useState<LimitOrderData | undefined>(undefined);

  // 🚀 LIMIT ORDER BALANCE VALIDATION STATE
  const [limitOrderBalanceError, setLimitOrderBalanceError] = useState<string>('');
  const [isLimitOrderBalanceValid, setIsLimitOrderBalanceValid] = useState<boolean>(true);

  // Privy hooks for wallet access
  const { authenticated, user } = usePrivy();
  const { wallets: solanaWallets } = useSolanaWallets();

  // Create a ref to store the latest fetchBalance function to avoid stale closures
  const fetchBalanceRef = useRef<() => void>();

  // Get Solana wallet address from defaultWallets localStorage (same as Pulse table page)
  const getSolanaWalletAddress = useCallback(() => {
    return getDefaultSolanaWalletAddress(authenticated, solanaWallets);
  }, [authenticated, solanaWallets]);

  // Fetch balance function (simplified, no complex debouncing)
  const fetchBalance = useCallback(async () => {
    // Check if user is authenticated
    if (!authenticated) {
      console.log('User not authenticated, cannot fetch balance');
      setBalanceData(null);
      return;
    }

    // Get wallet address
    const walletAddress = getSolanaWalletAddress();
    if (!walletAddress) {
      console.log('No Solana wallet connected, cannot fetch balance');
      setBalanceData(null);
      return;
    }

    // Validate Solana address format
    const isSolanaAddress = (address: string): boolean => {
      const base58Regex = /^[1-9A-HJ-NP-Za-km-z]{32,44}$/;
      return base58Regex.test(address);
    };

    if (!isSolanaAddress(walletAddress)) {
      console.error('Invalid Solana wallet address format:', walletAddress);
      setBalanceData(null);
      return;
    }

    try {
      // Get active token from localStorage
      const activePulseTokenStr = localStorage.getItem('activePulseToken');
      if (!activePulseTokenStr) {
        console.log('No active pulse token found');
        setBalanceData(null);
        return;
      }

      const activePulseToken: PulseTokenInfo = JSON.parse(activePulseTokenStr);

      // Validate token data
      if (!activePulseToken.address) {
        console.error('Active token missing address');
        setBalanceData(null);
        return;
      }

      // Check if token is on Solana network
      if (!activePulseToken.network?.toLowerCase().includes('solana')) {
        console.log('Token is not on Solana network:', activePulseToken.network);
        setBalanceData(null);
        return;
      }

      setIsLoadingBalance(true);

      const balanceRequest: BalanceRequest = {
        tokenAddress: activePulseToken.address,
        walletAddress: walletAddress
      };

      console.log('Fetching balance for:', {
        tokenAddress: activePulseToken.address,
        walletAddress: walletAddress
      });

      const response = await getTokenBalance(balanceRequest);
      setBalanceData(response);

      if (!response.success) {
        console.error('Balance fetch failed:', response.error);
      }

    } catch (error) {
      console.error('Error fetching balance:', error);
      setBalanceData(null);
    } finally {
      setIsLoadingBalance(false);
    }
  }, [authenticated, getSolanaWalletAddress]);

  // Update the ref whenever fetchBalance changes
  useEffect(() => {
    fetchBalanceRef.current = fetchBalance;
  }, [fetchBalance]);

  // Get display balance based on buy/sell mode
  const getDisplayBalance = useCallback(() => {
    // Check if user is authenticated and has wallet connected
    if (!authenticated) {
      return 'Connect Wallet';
    }

    if (!getSolanaWalletAddress()) {
      return 'No Solana Wallet';
    }

    // Check if balance data is available and successful
    if (!balanceData?.success) {
      return '0';
    }

    if (isBuy) {
      // Buy mode: show SOL balance
      const solBalance = parseFloat(balanceData.data.solBalance);
      return solBalance.toFixed(4);
    } else {
      // Sell mode: show token balance
      const tokenBalance = parseFloat(balanceData.data.tokenBalance);
      return tokenBalance > 1 ? Math.round(tokenBalance).toString() : tokenBalance.toFixed(6);
    }
  }, [authenticated, balanceData, isBuy, getSolanaWalletAddress]);

  const handleQuoteUpdate = useCallback((quote: QuoteResponse | null) => {
    setQuoteData(quote);
    if (quote?.success) {
      console.log('Quote received:', quote.data);
    } else if (quote?.error) {
      console.error('Quote error:', quote.error);
    }
  }, []);

  const handleLimitOrderDataUpdate = useCallback((data: LimitOrderData) => {
    setLimitOrderData(data);
  }, []);

  // 🚀 BALANCE VALIDATION FOR LIMIT ORDERS
  const validateLimitOrderBalance = useCallback((): { isValid: boolean; errorMessage: string } => {
    // Check if balance data is available
    if (!balanceData?.success) {
      return {
        isValid: false,
        errorMessage: 'Unable to verify balance. Please try again.'
      };
    }

    const requestedAmount = parseFloat(amount);

    // Validate amount is positive
    if (requestedAmount <= 0) {
      return {
        isValid: false,
        errorMessage: 'Please enter a valid amount greater than 0.'
      };
    }

    if (isBuy) {
      // Buy Order Validation: Check SOL balance
      const solBalance = parseFloat(balanceData.data.solBalance);

      if (solBalance < requestedAmount) {
        const shortfall = requestedAmount - solBalance;
        return {
          isValid: false,
          errorMessage: `Insufficient SOL balance. You have ${solBalance.toFixed(4)} SOL, but trying to spend ${requestedAmount} SOL. Shortfall: ${shortfall.toFixed(4)} SOL.`
        };
      }

      // Warn if spending more than 95% of SOL balance (but allow it)
      if (requestedAmount > solBalance * 0.95) {
        console.warn(`Limit order: Spending ${((requestedAmount / solBalance) * 100).toFixed(1)}% of SOL balance`);
      }
    } else {
      // Sell Order Validation: Check token balance
      const tokenBalance = parseFloat(balanceData.data.tokenBalance);

      if (tokenBalance < requestedAmount) {
        const shortfall = requestedAmount - tokenBalance;
        return {
          isValid: false,
          errorMessage: `Insufficient token balance. You have ${tokenBalance > 1 ? Math.round(tokenBalance) : tokenBalance.toFixed(6)} tokens, but trying to sell ${requestedAmount}. Shortfall: ${shortfall > 1 ? Math.round(shortfall) : shortfall.toFixed(6)}.`
        };
      }

      // Warn if selling more than 95% of balance (but allow it)
      if (requestedAmount > tokenBalance * 0.95) {
        console.warn(`Limit order: Selling ${((requestedAmount / tokenBalance) * 100).toFixed(1)}% of token balance`);
      }
    }

    return {
      isValid: true,
      errorMessage: ''
    };
  }, [balanceData, amount, isBuy]);

  // Real-time balance validation when amount or mode changes
  useEffect(() => {
    if (activeTab === 'Limit' && amount && parseFloat(amount) > 0) {
      const validation = validateLimitOrderBalance();
      setIsLimitOrderBalanceValid(validation.isValid);
      setLimitOrderBalanceError(validation.errorMessage);
    } else {
      // Reset validation state when amount is empty or not on limit tab
      setIsLimitOrderBalanceValid(true);
      setLimitOrderBalanceError('');
    }
  }, [activeTab, amount, isBuy, validateLimitOrderBalance]);

  // 🚀 REFRESH BALANCE WHEN AMOUNT CHANGES (for real-time validation)
  useEffect(() => {
    if (activeTab === 'Limit' && amount && parseFloat(amount) > 0 && authenticated) {
      // Debounce balance refresh to avoid excessive API calls
      const timeoutId = setTimeout(() => {
        if (fetchBalanceRef.current) {
          fetchBalanceRef.current();
        }
      }, 1000); // 1 second debounce

      return () => clearTimeout(timeoutId);
    }
  }, [amount, activeTab, authenticated]);

  // Main effect: Handle authentication, wallet, and initial balance fetch
  useEffect(() => {
    console.log('TradingPanel: Auth/wallet state changed');

    // Preload wallet info if user is authenticated
    if (authenticated && user?.id) {
      console.log('Preloading wallet info for authenticated user');
      preloadWalletInfo(user.id).catch(error => {
        console.error('Failed to preload wallet info:', error);
      });

      // Fetch balance when authenticated and has wallet
      const walletAddress = getSolanaWalletAddress();
      if (walletAddress) {
        console.log('TradingPanel: Fetching balance for wallet:', walletAddress);
        fetchBalance();
      }
    }
  }, [authenticated, user?.id]); // Removed getSolanaWalletAddress to prevent excessive calls

  // Reset amount and clear quote data when buy/sell mode changes
  useEffect(() => {
    // Reset amount when switching between buy/sell modes
    setAmount('0.0');

    // Clear existing quote data to prevent showing stale information
    setQuoteData(null);

    if (balanceData) {
      // Balance data is already available, just update display
      console.log('Buy/Sell mode changed, updating balance display, resetting amount, and clearing quote data');
    }
  }, [isBuy]);

  // Listen for activePulseToken changes and trade completion events (setup once)
  useEffect(() => {
    const handleStorageChange = () => {
      console.log('Active pulse token changed, refetching balance');
      // Use ref to access the latest fetchBalance function and avoid stale closures
      if (fetchBalanceRef.current) {
        fetchBalanceRef.current();
      }
    };

    const handleTradeSuccess = (event: CustomEvent) => {
      console.log('PulseTrade: Received pulseTradeSuccess event, refreshing balance', event.detail);
      // Refresh balance after successful transaction
      setTimeout(() => {
        if (fetchBalanceRef.current) {
          fetchBalanceRef.current();
        }
      }, 2000); // Additional 2 second delay for blockchain confirmation
    };

    // Listen for custom events when activePulseToken changes
    window.addEventListener('pulseDataChanged', handleStorageChange);
    window.addEventListener('pulseTradeSuccess', handleTradeSuccess as EventListener);

    return () => {
      window.removeEventListener('pulseDataChanged', handleStorageChange);
      window.removeEventListener('pulseTradeSuccess', handleTradeSuccess as EventListener);
    };
  }, []); // Empty dependency array - setup once

  // Reset UI state after successful swap
  const handleResetUI = useCallback(() => {
    console.log('Resetting UI state after successful swap');

    // Clear amount input
    setAmount('');

    // Clear quote data
    setQuoteData(null);

    // Auto-refresh balance after a short delay to allow transaction to settle
    setTimeout(() => {
      console.log('Auto-refreshing balance after swap');
      fetchBalance();
    }, 2000); // 2 second delay
  }, [fetchBalance]);

  // Handle swap completion with balance refresh
  const handleSwapComplete = useCallback((result: SwapResponse) => {
    console.log('Swap completed:', result);

    if (result.success) {
      // Auto-refresh balance after successful swap
      setTimeout(() => {
        console.log('Refreshing balance after successful swap');
        fetchBalance();
      }, 3000); // 3 second delay to allow blockchain confirmation
    }
  }, [fetchBalance]);

  // Optional: Set up periodic balance refresh (commented out to reduce API calls)
  // useEffect(() => {
  //   if (!authenticated || !getSolanaWalletAddress()) {
  //     return;
  //   }

  //   // Refresh balance every 60 seconds when user is active on the trade page
  //   const intervalId = setInterval(() => {
  //     console.log('PulseTrade: Periodic balance refresh');
  //     fetchBalance();
  //   }, 60000); // 60 seconds

  //   return () => {
  //     clearInterval(intervalId);
  //   };
  // }, [authenticated, getSolanaWalletAddress, fetchBalance]);

  // Cleanup: Cancel pending balance requests when component unmounts
  useEffect(() => {
    return () => {
      // Cancel any pending balance requests for this wallet/token combination
      const walletAddress = getSolanaWalletAddress();
      if (walletAddress) {
        const activePulseTokenStr = localStorage.getItem('activePulseToken');
        if (activePulseTokenStr) {
          try {
            const activePulseToken = JSON.parse(activePulseTokenStr);
            console.log('TradingPanel: Canceling pending balance requests on unmount');
            cancelBalanceRequest(walletAddress, activePulseToken.address);
          } catch (error) {
            console.error('Error parsing activePulseToken for cleanup:', error);
          }
        }
      }
    };
  }, []); // Empty dependency array - cleanup on unmount only

  const renderTabContent = () => {
    switch (activeTab) {
      case 'Market':
        return <MarketTab amount={amount} setAmount={setAmount} isBuy={isBuy} onQuoteUpdate={handleQuoteUpdate} balanceData={balanceData} />;
      case 'Limit':
        return <LimitTab amount={amount} setAmount={setAmount} isBuy={isBuy} onQuoteUpdate={handleQuoteUpdate} balanceData={balanceData} onLimitOrderDataUpdate={handleLimitOrderDataUpdate} />;
      case 'Adv.':
        return <AdvancedTab amount={amount} setAmount={setAmount} isBuy={isBuy} onQuoteUpdate={handleQuoteUpdate} balanceData={balanceData} />;
      default:
        return <MarketTab amount={amount} setAmount={setAmount} isBuy={isBuy} onQuoteUpdate={handleQuoteUpdate} balanceData={balanceData} />;
    }
  };

  return (
    <PresetProvider>
    <div className=" text-white min-h-screen max-w-md ">
      <div className="">
        <div className='border-b border-gray-700'>

        <BuySellToggle isBuy={isBuy} setIsBuy={setIsBuy} />
        </div>
        <div className='border-b border-gray-700'>
        <TabNavigation
          activeTab={activeTab}
          setActiveTab={setActiveTab}
          balance={getDisplayBalance()}
          isLoadingBalance={isLoadingBalance}
          isBuy={isBuy}
        />
        </div>
        <div className="p-4">

        {renderTabContent()}
        </div>
        <div className='px-4'>

        <BuyButton
          activeTab={activeTab}
          isBuy={isBuy}
          quoteData={quoteData}
          amount={amount}
          balanceData={balanceData}
          limitOrderData={limitOrderData}
          onSwapComplete={handleSwapComplete}
          onResetUI={handleResetUI}
          isLimitOrderBalanceValid={isLimitOrderBalanceValid}
          limitOrderBalanceError={limitOrderBalanceError}
        />
        </div>
        <PortfolioStats />
        <Preset/>
        <TokenInfo/>
      </div>


    </div>
    </PresetProvider>
  );
};

export default TradingPanel;