import React, { useState } from 'react';
import { ChevronUp, ChevronDown, DollarSign, RefreshCcw,ArrowLeftRight,Filter,User} from 'lucide-react';

const Trades = () => {
  const [activeTab, setActiveTab] = useState('TRADES');
  const [sortAsc, setSortAsc] = useState(true);
  const [showPrice, setShowPrice] = useState(false);
  const [showUSD, setShowUSD] = useState(false);

  const tradesData = [
    { amount: '1.2', usdAmount: '$20.4', mc: '$5.06K', price: '$0.052', trader: 'J2E', age: '3h', type: 'buy' },
    { amount: '0.55', usdAmount: '$9.35', mc: '$8.79K', price: '$0.075', trader: 'mKh', age: '3h', type: 'buy' },
  ];

  const devData = [
    { amount: '45.2', usdAmount: '$765.4', mc: '$12.3K', price: '$0.18', trader: 'Dev1', age: '1h', type: 'buy' },
  ];

  const youData = [
    { amount: '7.8', usdAmount: '$132.6', mc: '$5.67K', price: '$0.12', trader: 'YOU', age: '3h', type: 'buy' },
  ];

  const getCurrentData = () => {
    const data = activeTab === 'DEV' ? devData : activeTab === 'YOU' ? youData : tradesData;
    return [...data].sort((a, b) => {
      const aAge = parseInt(a.age.replace('h', ''));
      const bAge = parseInt(b.age.replace('h', ''));
      return sortAsc ? aAge - bAge : bAge - aAge;
    });
  };

  return (
    <div className="text-white max-h-[100rem] font-mono text-sm ">
      {/* Header Tabs */}
      <div className="flex items-center justify-between p-4 border-b border-gray-800">
  {/* Left side: TRADES */}
  <div>
    <button
      onClick={() => setActiveTab('TRADES')}
      className={`text-lg font-bold tracking-wide transition-colors ${
        activeTab === 'TRADES' ? 'text-white' : 'text-gray-500 hover:text-gray-300'
      }`}
    >
      Trades
    </button>
  </div>

  {/* Right side: DEV and YOU */}
  <div className="flex space-x-6">
    <button
      onClick={() => setActiveTab('DEV')}
      className={`flex items-center text-lg font-bold tracking-wide transition-colors ${
        activeTab === 'DEV' ? 'text-white' : 'text-gray-500 hover:text-gray-300'
      }`}
    >
      <Filter size={18} className="mr-1" />
      DEV
    </button>

    <button
      onClick={() => setActiveTab('YOU')}
      className={`flex items-center text-lg font-bold tracking-wide transition-colors ${
        activeTab === 'YOU' ? 'text-white' : 'text-gray-500 hover:text-gray-300'
      }`}
    >
      <User size={18} className="mr-1" />
      YOU
    </button>
  </div>
</div>

      {/* Column Headers */}
      <div className="flex items-center px-4 py-3 text-gray-400 text-xs border-b border-gray-800">
        {/* Amount / USD Toggle */}
        <div className="flex-1 flex items-center space-x-2">
          <span>Amount</span>
          <button
            onClick={() => setShowUSD(!showUSD)}
            className={`w-5 h-5 flex items-center justify-center rounded-full border transition-transform ${
              showUSD ? 'border-green-500 text-green-400' : 'border-gray-500 text-gray-400'
            } hover:scale-110`}
            title="Toggle USD/SOL"
          >
            <DollarSign size={10} />
          </button>
        </div>

        {/* Market Cap / Price Toggle */}
        <div className="w-24 flex items-center space-x-1 pl-4">
          <span>{showPrice ? 'Price' : 'MC'}</span>
          <button
  onClick={() => setShowPrice(!showPrice)}
  className="hover:text-white transition-colors"
  title="Toggle MC / Price"
>
  <ArrowLeftRight size={14} />
</button>

        </div>

        {/* Trader */}
        <div className="w-20 text-center">Trader</div>

        {/* Age Sort */}
        <div
          className="w-16 flex items-center justify-end cursor-pointer"
          onClick={() => setSortAsc(!sortAsc)}
        >
          <span>Age</span>
          {sortAsc ? <ChevronUp size={12} className="ml-1" /> : <ChevronDown size={12} className="ml-1" />}
        </div>
      </div>

      {/* Trade Entries */}
      <div className="divide-y divide-gray-800">
        {getCurrentData().map((trade, index) => (
          <div key={index} className="flex items-center px-4 py-2 hover:bg-gray-800/50 transition-colors">
            <div
              className={`flex-1 font-medium flex items-center space-x-1  ${
                trade.type === 'buy' ? 'text-green-400' : 'text-red-400'
              }`}
            >
              {!showUSD && (
                <img
                  src="https://metacore.mobula.io/78ee4d656f4f152a90d733f4eaaa4e1685e25bc654087acdb62bfe494d668976.png"
                  alt="sol"
                  className="w-3 h-3"
                />
              )}
              <span>{showUSD ? trade.usdAmount : trade.amount}</span>
            </div>
            <div className="w-24 text-gray-300 font-medium pl-4">{showPrice ? trade.price : trade.mc}</div>
            <div className="w-20 text-center text-gray-300 font-medium">{trade.trader}</div>
            <div className="w-16 text-right text-gray-400">{trade.age}</div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default Trades;
