import React, { useState, useEffect } from 'react';
import { X, Co<PERSON>, MessageCircle, RefreshCw } from 'lucide-react';

interface Token {
  name: string;
  symbol: string;
  price?: number;
  market_cap?: number;
  address?: string;
  liquidity?: number;
  volume_24h?: number;
  total_volume?: number;
  price_change_24h?: number;
  volume?: number;
  imageUrl?: string;
  exchange_logo?: string;
}

interface ShareModalProps {
  isOpen: boolean;
  onClose: () => void;
  token: Token;
}

const ShareModal: React.FC<ShareModalProps> = ({ isOpen, onClose, token }) => {
  const [copied, setCopied] = useState(false);
  const [chartData, setChartData] = useState<number[]>([]);
console.log('ShareModal token:', token);
  // Generate random chart data
  useEffect(() => {
    const data = Array.from({ length: 50 }, (_, i) => {
      const baseValue = 50;
      const noise = Math.sin(i * 0.1) * 10 + Math.random() * 5;
      return Math.max(10, Math.min(90, baseValue + noise));
    });
    setChartData(data);
  }, [isOpen]);

  if (!isOpen || !token) return null;

  const handleCopyLink = async () => {
    try {
      await navigator.clipboard.writeText(window.location.href);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error('Failed to copy link');
    }
  };

  const handleTelegramShare = () => {
    const text = `Check out ${token.name} (${token.symbol}) - Price: $${token.price?.toFixed(2)}`;
    const url = `https://t.me/share/url?url=${encodeURIComponent(window.location.href)}&text=${encodeURIComponent(text)}`;
    window.open(url, '_blank');
  };

  const changePercent = +(Math.random() * 10).toFixed(2);
  const currentTime = new Date().toLocaleString('en-US', { 
    hour12: false, 
    hour: '2-digit', 
    minute: '2-digit',
    month: 'short',
    day: '2-digit'
  });
  const formatNumber = (value: number) => {
    if (value >= 1_000_000_000) return `${(value / 1_000_000_000).toFixed(2)}B`;
    if (value >= 1_000_000) return `${(value / 1_000_000).toFixed(2)}M`;
    if (value >= 1_000) return `${(value / 1_000).toFixed(2)}K`;
    return value.toFixed(2);
  };
  const formatAddress = (address: string) =>
    `${address.slice(0, 6)}...${address.slice(-4)}`;
    
  return (
    <div className="fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50 p-4">
      <div className="bg-[#0a0a0a] rounded-3xl p-0 w-[420px] max-w-[95vw] border border-gray-800/50 shadow-2xl ">
        {/* Close Button - Top Right */}
        <div className="flex justify-end p-3">
  <button
    onClick={onClose}
    className="text-gray-400 hover:text-white transition-colors duration-200 p-2 hover:bg-gray-800/50 rounded-full"
  >
    <X size={20} />
  </button>
</div>


        {/* Header */}
        <div className="flex items-center justify-between px-6 pb-4">
          <div className="flex items-center gap-3">
            <div className="relative group">
              <div className="w-12 h-12 rounded-2xl bg-gradient-to-br from-gray-700 to-gray-800 flex items-center justify-center overflow-hidden">
                {token.imageUrl ? (
                  <img
                    src={token.imageUrl}
                    alt={token.name}
                    className="w-full h-full object-cover"
                  />
                ) : (
                  <span className="text-white text-lg font-bold">
                    {token.name.charAt(0)}
                  </span>
                )}
              </div>
            </div>
            <div>
              <h3 className="text-white font-bold text-xl tracking-tight">{token.name}</h3>
              <p className="text-gray-400 text-sm font-medium">{token.symbol}...pump</p>
            </div>
          </div>
          <div className="flex items-center gap-2">
           
            {/* SVG Icon on the right */}
            <svg width="18" height="25" viewBox="0 0 18 25" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M12.3454 0.71936H0.0898438V10.5215L3.34925 7.36022V4.12675H11.2747C12.9906 4.12675 14.381 5.57842 14.381 7.3698V10.9835C14.381 12.7749 12.9906 14.2266 11.2747 14.2266H5.49914L12.2248 7.40149H6.82746L0.0898438 14.1838V17.7143L6.78441 24.462H12.1993L5.46596 17.6318H12.4054C15.3331 17.5912 17.6877 15.0092 17.694 11.9518L17.7053 6.3271C17.7117 3.23216 15.3098 0.71936 12.3454 0.71936Z" fill="white"/>
</svg>

          </div>
        </div>

        {/* Price Section */}
        <div className="px-6 pb-6">
          <div className="flex items-center justify-between mb-4">
            <span className="text-gray-400 text-sm font-medium">24h change</span>
            <span className="text-gray-500 text-xs font-mono">{currentTime}</span>
          </div>
          
          <div className="text-green-400 text-4xl font-bold mb-6 tracking-tight">
            {token.price_change_24h}%
          </div>

          {/* Chart */}
          <div className="h-24 bg-[#111111] rounded-2xl mb-6 p-4 border border-gray-800/30">
            <svg width="100%" height="100%" className="overflow-visible">
              <defs>
                <linearGradient id="chartGradient" x1="0" y1="0" x2="0" y2="1">
                  <stop offset="0%" stopColor="#10B981" stopOpacity="0.3"/>
                  <stop offset="100%" stopColor="#10B981" stopOpacity="0"/>
                </linearGradient>
              </defs>
              {chartData.length > 0 && (
                <>
                  <path
                    d={`M ${chartData.map((value, index) => 
                      `${(index / (chartData.length - 1)) * 100}% ${100 - value}%`
                    ).join(' L ')}`}
                    fill="none"
                    stroke="#10B981"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="drop-shadow-sm"
                  />
                  <path
                    d={`M ${chartData.map((value, index) => 
                      `${(index / (chartData.length - 1)) * 100}% ${100 - value}%`
                    ).join(' L ')} L 100% 100% L 0% 100% Z`}
                    fill="url(#chartGradient)"
                  />
                </>
              )}
            </svg>
          </div>

          <div className="flex items-baseline gap-2">
            <span className="text-gray-400 text-sm font-medium">Price</span>
            <span className="text-white text-2xl font-bold tracking-tight">
              ${token.price?.toFixed(2) || '63.48'}
            </span>
          </div>
        </div>

        {/* Stats Grid */}
        <div className="px-6 pb-6">
          <div className="space-y-4">
          {[
  { label: 'Mcap', value: token.market_cap },
  { label: 'Liquidity', value: token.liquidity },
  { label: '24h Vol', value: token.volume }
].map((stat, index) => (
  <div key={index} className="flex justify-between items-center py-1">
    <span className="text-gray-400 font-medium">{stat.label}</span>
    <span className="text-white font-bold">
      ${formatNumber(stat.value || 215.06)}
    </span>
  </div>
))}

          </div>
        </div>

        {/* Invite Section - Now above action buttons */}
        <div className="px-6 pb-6">
          <div className="bg-[#111111] rounded-2xl p-4 border border-gray-800/30">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-gradient-to-br from-gray-600 to-gray-700 rounded-full flex items-center justify-center">
                  <span className="text-white text-xs font-bold">0x</span>
                </div>
                <div>
                  <div className="text-white font-medium text-sm">{formatAddress(token.address || '')}</div>
                  <div className="text-gray-400 text-xs font-medium">Invite Code: ZVMAEA</div>
                </div>
              </div>
              <div className="text-gray-400 text-xs max-w-[120px] text-right leading-tight">
                Trade any token on any chain
              </div>
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex justify-center items-center gap-12 pb-8">
          <button
            onClick={handleCopyLink}
            className="flex flex-col items-center gap-3 text-gray-300 hover:text-white transition-all duration-200 group"
          >
            <div className="w-14 h-14 bg-[#1a1a1a] hover:bg-[#252525] rounded-2xl flex items-center justify-center transition-all duration-200 group-hover:scale-105 border border-gray-800/50">
              <Copy size={22} className={copied ? 'text-green-400' : ''} />
            </div>
            <span className="text-sm font-medium">{copied ? 'Copied!' : 'Copy Link'}</span>
          </button>

          <button
            // onClick={onClose}
            className="flex flex-col items-center gap-3 text-gray-300 hover:text-white transition-all duration-200 group"
          >
            <div className="w-14 h-14 bg-[#1a1a1a] hover:bg-[#252525] rounded-2xl flex items-center justify-center transition-all duration-200 group-hover:scale-105 border border-gray-800/50">
              <X size={22} />
            </div>
            <span className="text-sm font-medium">Twitter</span>
          </button>

          <button
            onClick={handleTelegramShare}
            className="flex flex-col items-center gap-3 text-gray-300 hover:text-white transition-all duration-200 group"
          >
            <div className="w-14 h-14 bg-[#1a1a1a] hover:bg-[#252525] rounded-2xl flex items-center justify-center transition-all duration-200 group-hover:scale-105 border border-gray-800/50">
              <MessageCircle size={22} />
            </div>
            <span className="text-sm font-medium">Telegram</span>
          </button>
        </div>
      </div>
    </div>
  );
};

export default ShareModal;