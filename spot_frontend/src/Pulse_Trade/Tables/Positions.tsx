import React from 'react';
import { TrendingUp, TrendingDown, Settings } from 'lucide-react';

interface TokenData {
  id: string;
  token: string;
  bought: number;
  sold: number;
  remaining: number;
  pnl: number;
}

const mockTokens: TokenData[] = [
  { id: '1', token: 'BTC', bought: 50000, sold: 35000, remaining: 15000, pnl: 12500 },
  { id: '2', token: 'ETH', bought: 25000, sold: 18000, remaining: 7000, pnl: -2300 },
  { id: '3', token: 'SOL', bought: 15000, sold: 8000, remaining: 7000, pnl: 4200 },
];

const formatCurrency = (value: number) =>
  new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(value);

const getPnLColor = (pnl: number) =>
  pnl >= 0 ? 'text-green-400' : 'text-red-400';

const getPnLIcon = (pnl: number) =>
  pnl >= 0 ? <TrendingUp size={14} /> : <TrendingDown size={14} />;

const Positions: React.FC = () => {
  return (
    <div className=" rounded-lg overflow-hidden">
      <div className="overflow-x-auto">
        <table className="w-full">
          <thead className="">
            <tr className="border-b border-neutral-700">
              <th className="text-left px-6 py-3 text-sm font-medium text-neutral-300">Token</th>
              <th className="text-right px-4 py-3 text-sm font-medium text-neutral-300">Bought</th>
              <th className="text-right px-4 py-3 text-sm font-medium text-neutral-300">Sold</th>
              <th className="text-right px-4 py-3 text-sm font-medium text-neutral-300">Remaining</th>
              <th className="text-right px-4 py-3 text-sm font-medium text-neutral-300">PnL</th>
              <th className="text-right px-4 py-3 text-sm font-medium text-neutral-300">
                <div className="flex items-center justify-end gap-1">
                 Actions
                  
                </div>
              </th>
            </tr>
          </thead>
          <tbody className="divide-y divide-neutral-800/50">
            {mockTokens.map((token) => (
              <tr key={token.id} className="hover:bg-neutral-800/30 transition-colors">
                <td className="px-6 py-4">
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 rounded-full bg-gradient-to-r from-blue-500 to-purple-500 flex items-center justify-center text-sm text-white font-bold">
                      {token.token.slice(0, 2)}
                    </div>
                    <span className="font-medium text-white">{token.token}</span>
                  </div>
                </td>
                <td className="px-4 py-4 text-sm text-white font-mono text-right">
                  {formatCurrency(token.bought)}
                </td>
                <td className="px-4 py-4 text-sm text-white font-mono text-right">
                  {formatCurrency(token.sold)}
                </td>
                <td className="px-4 py-4 text-sm text-white font-mono text-right">
                  {formatCurrency(token.remaining)}
                </td>
                <td className={`px-4 py-4 text-sm font-mono text-right ${getPnLColor(token.pnl)}`}>
                  <div className="flex items-center justify-end gap-1">
                    {getPnLIcon(token.pnl)}
                    {formatCurrency(token.pnl)}
                  </div>
                </td>
                <td className="px-4 py-4 text-right">
                  <button className="text-neutral-500 hover:text-neutral-300 transition-colors">
                    
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default Positions;
