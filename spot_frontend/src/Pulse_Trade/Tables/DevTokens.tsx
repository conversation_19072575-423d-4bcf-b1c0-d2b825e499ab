import { useState } from 'react';
import { ChevronDown, Copy, ExternalLink } from 'lucide-react';

const mockDevTokensData = [
  {
    id: 1,
    token: 'MBC',
    logo: '🔥',
    age: '1d',
    marketCap: 1711052487681,
    remaining: { amount: 0.00, percentage: 0.0 },
    holders: 2,
    devPnl: null,
    bondingProgress: 100.0,
    contractAddress: '8x7f3a2b5c9d1e4f6a8b2c5d9e1f4a7b3c6d9e2f5a8b1c4d7e0f3a6b9c2d5e8f1a4'
  },
  {
    id: 2,
    token: 'TKL',
    logo: '⚡',
    age: '6h',
    marketCap: 331553969563,
    remaining: { amount: 777.42, percentage: 78 },
    holders: 5,
    devPnl: -0.1736,
    bondingProgress: 100.0,
    contractAddress: '9x8e4b7a1d5c2f9e6b3a8d1c4f7e0b5a2d9c6e3f8b1a4d7c0e5f2a9b6c3d8e1f4a'
  },
  {
    id: 3,
    token: 'MTK',
    logo: '💎',
    age: '2d',
    marketCap: 887553101093,
    remaining: { amount: 0.00, percentage: 0.0 },
    holders: 3,
    devPnl: null,
    bondingProgress: 100.0,
    contractAddress: '7x6c9f2a5b8e1d4c7f0a3b6e9d2c5f8a1b4e7d0c3f6a9b2e5d8c1f4a7b0e3d6c9f'
  },
  {
    id: 1,
    token: 'MBC',
    logo: '🔥',
    age: '1d',
    marketCap: 1711052487681,
    remaining: { amount: 0.00, percentage: 0.0 },
    holders: 2,
    devPnl: null,
    bondingProgress: 100.0,
    contractAddress: '8x7f3a2b5c9d1e4f6a8b2c5d9e1f4a7b3c6d9e2f5a8b1c4d7e0f3a6b9c2d5e8f1a4'
  },
  {
    id: 2,
    token: 'TKL',
    logo: '⚡',
    age: '6h',
    marketCap: 331553969563,
    remaining: { amount: 777.42, percentage: 78 },
    holders: 5,
    devPnl: -0.1736,
    bondingProgress: 100.0,
    contractAddress: '9x8e4b7a1d5c2f9e6b3a8d1c4f7e0b5a2d9c6e3f8b1a4d7c0e5f2a9b6c3d8e1f4a'
  },
  {
    id: 3,
    token: 'MTK',
    logo: '💎',
    age: '2d',
    marketCap: 887553101093,
    remaining: { amount: 0.00, percentage: 0.0 },
    holders: 3,
    devPnl: null,
    bondingProgress: 100.0,
    contractAddress: '7x6c9f2a5b8e1d4c7f0a3b6e9d2c5f8a1b4e7d0c3f6a9b2e5d8c1f4a7b0e3d6c9f'
  },
  {
    id: 1,
    token: 'MBC',
    logo: '🔥',
    age: '1d',
    marketCap: 1711052487681,
    remaining: { amount: 0.00, percentage: 0.0 },
    holders: 2,
    devPnl: null,
    bondingProgress: 100.0,
    contractAddress: '8x7f3a2b5c9d1e4f6a8b2c5d9e1f4a7b3c6d9e2f5a8b1c4d7e0f3a6b9c2d5e8f1a4'
  },
  {
    id: 2,
    token: 'TKL',
    logo: '⚡',
    age: '6h',
    marketCap: 331553969563,
    remaining: { amount: 777.42, percentage: 78 },
    holders: 5,
    devPnl: -0.1736,
    bondingProgress: 100.0,
    contractAddress: '9x8e4b7a1d5c2f9e6b3a8d1c4f7e0b5a2d9c6e3f8b1a4d7c0e5f2a9b6c3d8e1f4a'
  },
  {
    id: 3,
    token: 'MTK',
    logo: '💎',
    age: '2d',
    marketCap: 887553101093,
    remaining: { amount: 0.00, percentage: 0.0 },
    holders: 3,
    devPnl: null,
    bondingProgress: 100.0,
    contractAddress: '7x6c9f2a5b8e1d4c7f0a3b6e9d2c5f8a1b4e7d0c3f6a9b2e5d8c1f4a7b0e3d6c9f'
  },
  {
    id: 1,
    token: 'MBC',
    logo: '🔥',
    age: '1d',
    marketCap: 1711052487681,
    remaining: { amount: 0.00, percentage: 0.0 },
    holders: 2,
    devPnl: null,
    bondingProgress: 100.0,
    contractAddress: '8x7f3a2b5c9d1e4f6a8b2c5d9e1f4a7b3c6d9e2f5a8b1c4d7e0f3a6b9c2d5e8f1a4'
  },
  {
    id: 2,
    token: 'TKL',
    logo: '⚡',
    age: '6h',
    marketCap: 331553969563,
    remaining: { amount: 777.42, percentage: 78 },
    holders: 5,
    devPnl: -0.1736,
    bondingProgress: 100.0,
    contractAddress: '9x8e4b7a1d5c2f9e6b3a8d1c4f7e0b5a2d9c6e3f8b1a4d7c0e5f2a9b6c3d8e1f4a'
  },
  {
    id: 3,
    token: 'MTK',
    logo: '💎',
    age: '2d',
    marketCap: 887553101093,
    remaining: { amount: 0.00, percentage: 0.0 },
    holders: 3,
    devPnl: null,
    bondingProgress: 100.0,
    contractAddress: '7x6c9f2a5b8e1d4c7f0a3b6e9d2c5f8a1b4e7d0c3f6a9b2e5d8c1f4a7b0e3d6c9f'
  }
];

export default function DevTokens() {
  const [sortConfig, setSortConfig] = useState({ key: 'marketCap', direction: 'desc' });

  const formatCurrency = (value: number) => {
    if (value >= 1e12) return `$${(value / 1e12).toFixed(2)}T`;
    if (value >= 1e9) return `$${(value / 1e9).toFixed(2)}B`;
    if (value >= 1e6) return `$${(value / 1e6).toFixed(2)}M`;
    if (value >= 1e3) return `$${(value / 1e3).toFixed(2)}K`;
    return `$${value.toFixed(2)}`;
  };

  const formatNumber = (value: number) => {
    if (value >= 1e6) return `${(value / 1e6).toFixed(2)}M`;
    if (value >= 1e3) return `${(value / 1e3).toFixed(2)}K`;
    return value.toFixed(2);
  };

  const handleSort = (key: string) => {
    let direction = 'asc';
    if (sortConfig.key === key && sortConfig.direction === 'asc') {
      direction = 'desc';
    }
    setSortConfig({ key, direction });
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
  };

  const truncateAddress = (address: string) => {
    return `${address.slice(0, 6)}...${address.slice(-4)}`;
  };

  const sortedData = [...mockDevTokensData].sort((a, b) => {
    if (sortConfig.key === 'marketCap') {
      return sortConfig.direction === 'asc' ? a.marketCap - b.marketCap : b.marketCap - a.marketCap;
    }
    return 0;
  });

  return (
<div className="rounded-lg overflow-hidden">
  {/* Fixed Header */}
  <div className="overflow-x-auto">
    <table className="w-full">
      <thead className="bg-neutral-900/70 backdrop-blur-md">
        <tr className="border-b border-neutral-700">
          <th className="text-left px-6 py-3 text-sm font-medium text-neutral-300 min-w-[200px]">
            Token
          </th>
          <th className="text-left px-4 py-3 text-sm font-medium text-neutral-300 min-w-[80px]">
            Age
          </th>
          <th 
            className="text-left px-4 py-3 text-sm font-medium text-neutral-300 cursor-pointer hover:text-white transition-colors min-w-[120px]"
            onClick={() => handleSort('marketCap')}
          >
            <div className="flex items-center gap-1">
              <ChevronDown size={14} className={sortConfig.direction === 'desc' ? 'rotate-0' : 'rotate-180'} />
              Market cap
            </div>
          </th>
          <th className="text-left px-4 py-3 text-sm font-medium text-neutral-300 min-w-[120px]">
            Remaining
          </th>
          <th className="text-left px-4 py-3 text-sm font-medium text-neutral-300 min-w-[80px] text-center">
            Holders
          </th>
          <th className="text-left px-4 py-3 text-sm font-medium text-neutral-300 min-w-[100px]">
            Dev PNL
          </th>
          <th className="text-left px-4 py-3 text-sm font-medium text-neutral-300 min-w-[180px]">
            Bonding curve progress
          </th>
        </tr>
      </thead>
    </table>
  </div>

  {/* Scrollable Body */}
  <div className="max-h-[600px] overflow-y-auto overflow-x-auto">
    <table className="w-full">
      <tbody className="divide-y divide-neutral-800">
        {sortedData.map((token) => (
          <tr key={token.id} className="hover:bg-neutral-800/50 transition-colors">
            {/* Token */}
            <td className="px-6 py-4 min-w-[200px]">
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 rounded-full bg-gradient-to-r from-blue-500 to-purple-500 flex items-center justify-center text-sm flex-shrink-0">
                  {token.logo}
                </div>
                <div className="min-w-0">
                  <div className="font-medium text-white truncate">{token.token}</div>
                  <div className="flex items-center gap-2 mt-1">
                    <span className="text-xs text-neutral-400 font-mono truncate">
                      {truncateAddress(token.contractAddress)}
                    </span>
                    <button
                      onClick={() => copyToClipboard(token.contractAddress)}
                      className="text-neutral-500 hover:text-neutral-300 transition-colors flex-shrink-0"
                    >
                      <Copy size={12} />
                    </button>
                    <button className="text-neutral-500 hover:text-neutral-300 transition-colors flex-shrink-0">
                      <ExternalLink size={12} />
                    </button>
                  </div>
                </div>
              </div>
            </td>

            {/* Age */}
            <td className="px-4 py-4 text-sm text-neutral-300 min-w-[80px]">{token.age}</td>

            {/* Market Cap */}
            <td className="px-4 py-4 text-sm font-mono text-white min-w-[120px]">
              {formatCurrency(token.marketCap)}
            </td>

            {/* Remaining */}
            <td className="px-4 py-4 min-w-[120px]">
              <div className="text-sm">
                <span className="text-white font-mono">
                  {formatNumber(token.remaining.amount)}
                </span>
                <span className="text-neutral-400 ml-2">{token.remaining.percentage}%</span>
              </div>
            </td>

            {/* Holders */}
            <td className="px-4 py-4 text-sm text-white text-center min-w-[80px]">
              {token.holders}
            </td>

            {/* Dev PNL */}
            <td className="px-4 py-4 text-sm min-w-[100px]">
              {token.devPnl !== null ? (
                <span className={token.devPnl < 0 ? 'text-red-400' : 'text-green-400'}>
                  {token.devPnl < 0 ? '' : '+'}{(token.devPnl * 100).toFixed(2)}%
                </span>
              ) : (
                <span className="text-neutral-500">-</span>
              )}
            </td>

            {/* Bonding Curve Progress */}
            <td className="px-4 py-4 min-w-[180px]">
              <div className="flex items-center gap-3">
                <div className="flex-1 bg-neutral-700 rounded-full h-2 min-w-[100px]">
                  <div
                    className="bg-green-500 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${token.bondingProgress}%` }}
                  />
                </div>
                <span className="text-sm text-white font-medium min-w-[3rem] flex-shrink-0">
                  {token.bondingProgress.toFixed(1)}%
                </span>
              </div>
            </td>
          </tr>
        ))}
      </tbody>
    </table>
  </div>
</div>
  );
}