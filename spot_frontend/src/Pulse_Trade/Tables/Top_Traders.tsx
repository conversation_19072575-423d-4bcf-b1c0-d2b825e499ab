import { useState } from 'react';

const mockHoldersData = [
  {
    id: 1,
    rank: 1,
    address: 'z9qnqh2M',

    amount: '990.00M',
    bought: { value: 13.73, txns: 1 },
    sold: { value: 0.00, txns: 0 },
    pnl: -13.73,
    walletAddress: '007211138.620',
    remaining: '99%',
    activity: '1d ago'
  },
  {
    id: 2,
    rank: 2,
    address: 'x5m8pL9K',

    amount: '750.50M',
    bought: { value: 25.80, txns: 3 },
    sold: { value: 12.40, txns: 2 },
    pnl: 45.20,
    walletAddress: '005847229.410',
    remaining: '87%',
    activity: '3h ago'
  },
  {
    id: 3,
    rank: 3,
    address: 'a7Qr3nH4',
    amount: '425.75M',
    bought: { value: 18.95, txns: 2 },
    sold: { value: 8.30, txns: 1 },
    pnl: -2.45,
    walletAddress: '003921847.230',
    remaining: '65%',
    activity: '6h ago'
  },
  {
    id: 4,
    rank: 4,
    address: 'p2Bn7xM9',
    
    amount: '320.25M',
    bought: { value: 32.15, txns: 5 },
    sold: { value: 15.60, txns: 3 },
    pnl: 78.90,
    walletAddress: '002845193.180',
    remaining: '52%',
    activity: '12h ago'
  },
  {
    id: 5,
    rank: 5,
    address: 'k8Fv4tL2',
  
    amount: '180.90M',
    bought: { value: 8.45, txns: 1 },
    sold: { value: 22.10, txns: 4 },
    pnl: -5.75,
    walletAddress: '001632847.095',
    remaining: '28%',
    activity: '2d ago'
  },
  
  
  
];

export default function Top_Traders() {
  const [sortConfig, setSortConfig] = useState({ key: 'rank', direction: 'asc' });

  const formatCurrency = (value: number, decimals = 2) => {
    return `$${value.toFixed(decimals)}`;
  };

  const formatAmount = (amount: string) => {
    return amount;
  };

  const handleSort = (key: string) => {
    let direction = 'asc';
    if (sortConfig.key === key && sortConfig.direction === 'asc') {
      direction = 'desc';
    }
    setSortConfig({ key, direction });
  };



  const getPnlColor = (pnl: number) => {
    return pnl >= 0 ? 'text-green-400' : 'text-red-400';
  };

  const sortedData = [...mockHoldersData].sort((a, b) => {
    if (sortConfig.key === 'rank') {
      return sortConfig.direction === 'asc' ? a.rank - b.rank : b.rank - a.rank;
    }
    return 0;
  });

  return (
    <div className="h-full flex flex-col">
      <div className="overflow-x-auto">
        <table className="w-full">
          <thead className="sticky top-0 z-10">
            <tr className="border-b border-neutral-700">
              <th 
                className="text-left px-4 py-3 text-sm font-medium text-neutral-300 cursor-pointer hover:text-white transition-colors"
                onClick={() => handleSort('rank')}
              >
                <div className="flex items-center gap-1">
                  Rank
                </div>
              </th>
              <th className="text-left px-4 py-3 text-sm font-medium text-neutral-300">
                Address
              </th>
              <th className="text-right px-4 py-3 text-sm font-medium text-neutral-300">
                Amount
              </th>
              <th className="text-right px-4 py-3 text-sm font-medium text-neutral-300">
                Bought
              </th>
              <th className="text-right px-4 py-3 text-sm font-medium text-neutral-300">
                Sold
              </th>
              <th className="text-right px-4 py-3 text-sm font-medium text-neutral-300">
                PNL
              </th>
              <th className="text-right px-4 py-3 text-sm font-medium text-neutral-300">
                Remaining
              </th>
              <th className="text-right px-4 py-3 text-sm font-medium text-neutral-300">
                <div className="flex items-center justify-end gap-1">
                  Activity
                  
                </div>
              </th>
            </tr>
          </thead>
          </table>
          </div>
          <div className="flex-1 overflow-y-auto">
          <table className="w-full">
          <tbody className="divide-y divide-neutral-800/50 ">
            {sortedData.map((holder) => (
              <tr key={holder.id} className="hover:bg-neutral-800/30 transition-colors">
                {/* Rank */}
                <td className="px-4 py-4 text-sm text-white font-medium">
                  {holder.rank}
                </td>

                {/* Address */}
                <td className="px-4 py-4">
                  <div className="flex items-center gap-2">
                    <div className="flex items-center gap-1">
  
                      <span className="text-sm text-white font-mono">{holder.address}</span>

                    </div>
                  </div>
                </td>

                {/* Amount */}
                <td className="px-4 py-4 text-sm text-white font-mono text-right">
                  {formatAmount(holder.amount)}
                </td>

                {/* Bought */}
                <td className="px-4 py-4 text-right">
                  <div className="text-sm">
                    <div className="text-green-400 font-medium">
                      {formatCurrency(holder.bought.value)}
                    </div>
                    <div className="text-xs text-neutral-400">
                      {holder.bought.txns} txn{holder.bought.txns !== 1 ? 's' : ''}
                    </div>
                  </div>
                </td>

                {/* Sold */}
                <td className="px-4 py-4 text-right">
                  <div className="text-sm">
                    <div className="text-red-400 font-medium">
                      {formatCurrency(holder.sold.value)}
                    </div>
                    <div className="text-xs text-neutral-400">
                      {holder.sold.txns} txn{holder.sold.txns !== 1 ? 's' : ''}
                    </div>
                  </div>
                </td>

                {/* PNL */}
                <td className="px-4 py-4 text-right">
                  <div className="text-sm">
                    <div className={`font-medium ${getPnlColor(holder.pnl)}`}>
                      {holder.pnl >= 0 ? '+' : ''}{formatCurrency(holder.pnl)}
                    </div>
                    <div className="text-xs text-neutral-400 font-mono">
                      {holder.walletAddress}
                    </div>
                  </div>
                </td>

                {/* Remaining */}
                <td className="px-4 py-4 text-sm text-white font-medium text-right">
                  {holder.remaining}
                </td>

                {/* Activity */}
                <td className="px-4 py-4 text-sm text-neutral-400 text-right">
                  {holder.activity}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      
    </div>
  );
}