// Define the shared properties across all trade-related tables
export interface BaseTradeData {
  id?: string;
  wallet_address: string;
  ticker: string;
  timestamp: string | Date;
}

// Completed trades
export interface Trade extends BaseTradeData {
  qty: number | string;
  entry_price: number | string;
  exit_price: number | string;
  entry_value: number | string;
  exit_value: number | string;
  closed_pnl: number | string;
  dex: string;
  tx_hash?: string;
}

// Pending/active orders
export interface Order extends BaseTradeData {
  type: 'Market' | 'Limit';
  qty: number | string;
  share: number | string; // percentage of portfolio
  value: number | string;
  target_price: number | string;
  target_mktcap?: number | string;
  target_pnl: number | string;
  status: 'Open' | 'Filled' | 'Canceled';
}

// Historical trade activities
export interface TradeHistory extends BaseTradeData {
  trade_type: 'Buy' | 'Sell';
  qty: number | string;
  value: number | string;
  filled_price: number | string;
  tx_hash: string;
  dex: string;
  token_in_symbol: string;
  token_out_symbol: string;
  token_in_address: string;
  token_out_address: string;
  amount_in: string;
  amount_out: string;
  platform_fee_included?: boolean;
  bundled_transaction?: boolean;
}
