import React, { useState, useEffect, useRef, MouseEvent } from 'react';
import {usePulseData} from '@/hooks/usePulseData';
import { useWebSocketPulseData } from '@/hooks/useWebSocketPulseData';
import Display from './Options/Display';
import QuickBuy from './Options/QuickBuy';
import { activityService } from '@/services/activityService';
import {
  Users,
  User,
  Settings,
  TrendingUp,
  Activity,
  Zap,
  SlidersHorizontal,
  Twitter,
  Globe,
  Send,
  Shield,
  Target,
  Package,
  Copy,
  Clipboard,
  AlertTriangle
} from 'lucide-react';
import clsx from "clsx";
import TradingSettingsModal from './TradingSettiingsModal';
import Footer from '../Footer/Footer';
import { homeAPI } from '@/utils/api';
import { useNavigate } from 'react-router-dom';
import TokenAge from '@/components/TokenAge';
import { usePrivy } from '@privy-io/react-auth';
import { useSolanaWallets } from '@privy-io/react-auth/solana';
import {
  getTokenBalance,
  getSwapForExchange,
  SwapRequest,
  BalanceRequest,
  cancelBalanceRequest
} from '../../api/solana_api';
import { getPrivySolanaWalletInfo, getCachedWalletId } from '../../api/privy_api';
import { showSwapSuccessToast, showSwapErrorToast, showSwapInfoToast } from '../../utils/swapToast';
import { getDefaultSolanaWalletAddress, getDefaultSolanaWalletInfo } from '../../utils/walletUtils';

import {toast} from 'react-toastify'




interface TokenType {
  id?: string;
  address: string;
  name: string;
  symbol: string;
  network?: string;
  [key: string]: any;
}

interface StatsType {
  holders: string;
  buys: number;
  sells: number;
  proTraders: string;
  devMigrations: string;
  top10Holders: string;
  devHolding: string;
  snipers: string;
  insiders: string;
  bundlers: string;
  dexPaid: string;
}

interface SocialsType {
  telegram: string;
  website: string;
}

interface CustomRowsType {
  marketCap: boolean;
  volume: boolean;
  tx: boolean;
  socials: boolean;
  holders: boolean;
  proTraders: boolean;
  devMigrations: boolean;
}

interface SettingsType {
  circleImages: boolean;
  progressBar: boolean;
  greyButtons: boolean;
 
}

interface CryptoCardProps {
  imageUrl: string;
  exchange_logo: string;
  name: string;
  symbol: string;
  age: string;
  createdAt: string | null; // Add createdAt field
  marketCap: number;
  volume: number;
  txCount: string;
  price: number;
  bonding: number;
  address: string;
  network: string; // ✅ Added network field to CryptoCardProps
  socials: SocialsType;
  stats: StatsType;
  amount: string;
  quickBuySize: string;
  supply: string;
  liquidity: string;
  customRows: CustomRowsType;
  settings: SettingsType;
  token: TokenType;
  metricsSize: string;
}




const CryptoCard: React.FC<CryptoCardProps> = ({
  imageUrl,
  name: _name,
  token,
  symbol: _symbol,
  age,
  createdAt, // Add createdAt prop
  marketCap,
  volume,
  bonding,
  txCount,
  address,
  stats,
  quickBuySize,
  amount,
  exchange_logo,
  customRows,
  settings,
  metricsSize
}) => {
  const navigate = useNavigate();
  const [isExecutingBuy, setIsExecutingBuy] = useState(false);
  const [imageError, setImageError] = useState(false);
  const [isImageLoading, setIsImageLoading] = useState(true);

  // Privy hooks for wallet access
  const { authenticated, user } = usePrivy();
  const { wallets: solanaWallets } = useSolanaWallets();

  const {setActiveToken}= usePulseData()

  // Get first letter of token name or symbol for fallback
  const getTokenInitial = () => {
    if (_name && _name.length > 0) {
      return _name.charAt(0).toUpperCase();
    } else if (_symbol && _symbol.length > 0) {
      return _symbol.charAt(0).toUpperCase();
    }
    return "T"; // Default fallback
  };

  // Generate a consistent background color based on token address
  const getBackgroundColor = () => {
    if (!address) return "hsl(210, 70%, 50%)"; // Default blue
    
    // Simple hash function to generate a consistent color
    const hash = address.split('').reduce((acc, char) => {
      return char.charCodeAt(0) + ((acc << 5) - acc);
    }, 0);
    
    // Generate hue between 0-360 degrees
    const hue = Math.abs(hash) % 360;
    
    return `hsl(${hue}, 70%, 50%)`;
  };

  const handleImageLoad = () => {
    setIsImageLoading(false);
  };

  const handleImageError = () => {
    setImageError(true);
    setIsImageLoading(false);
  };

  const handleRowClick = (e: MouseEvent<HTMLDivElement>, token: TokenType) => {
    setActiveToken(e, token);
    navigate(`/trade/${token.address}`);
  };

  // Get Solana wallet address from defaultWallets localStorage (same as Pulse trade page)
  const getSolanaWalletAddress = () => {
    if (!authenticated) {
      return null;
    }

    // First try to get from defaultWallets localStorage
    try {
      const storedDefaults = localStorage.getItem('defaultWallets');
      if (storedDefaults) {
        const defaultWallets = JSON.parse(storedDefaults);
        if (defaultWallets.solana) {
          console.log('Using default Solana wallet from localStorage:', defaultWallets.solana);
          return defaultWallets.solana;
        }
      }
    } catch (error) {
      console.error('Error reading defaultWallets from localStorage:', error);
    }

    // Fallback to Privy hooks if no default wallet set
    if (solanaWallets.length === 0) {
      return null;
    }

    const solanaWallet = solanaWallets[0];
    console.log('Using fallback Solana wallet from Privy:', solanaWallet?.address);
    return solanaWallet?.address || null;
  };

  // Get Solana wallet info for swap using defaultWallets localStorage (same as Pulse trade page)
  const getSolanaWalletInfo = async () => {
    if (!authenticated || !user?.id) {
      return null;
    }

    // First try to get wallet address from defaultWallets localStorage
    let walletAddress: string | null = null;
    try {
      const storedDefaults = localStorage.getItem('defaultWallets');
      if (storedDefaults) {
        const defaultWallets = JSON.parse(storedDefaults);
        if (defaultWallets.solana) {
          walletAddress = defaultWallets.solana;
          console.log('Using default Solana wallet from localStorage for swap:', walletAddress);
        }
      }
    } catch (error) {
      console.error('Error reading defaultWallets from localStorage:', error);
    }

    // Fallback to Privy hooks if no default wallet set
    if (!walletAddress) {
      if (solanaWallets.length === 0) {
        return null;
      }

      const solanaWallet = solanaWallets[0];
      if (!solanaWallet.address) {
        return null;
      }

      walletAddress = solanaWallet.address;
      console.log('Using fallback Solana wallet from Privy for swap:', walletAddress);
    }

    // Try to get cached wallet ID first
    const cachedWalletId = getCachedWalletId(user.id, walletAddress);
    if (cachedWalletId) {
      return {
        address: walletAddress,
        id: cachedWalletId
      };
    }

    // Fallback to Privy API
    try {
      const privyResponse = await getPrivySolanaWalletInfo(user.id);
      if (privyResponse.success && privyResponse.data) {
        if (privyResponse.data.address === walletAddress) {
          return {
            address: walletAddress,
            id: privyResponse.data.walletId
          };
        }
      }
    } catch (error) {
      console.error('Error fetching wallet ID:', error);
    }

    // Fallback ID
    const fallbackId = `solana_${walletAddress.slice(0, 8)}_${walletAddress.slice(-8)}`;
    return {
      address: walletAddress,
      id: fallbackId
    };
  };

  // Execute quick buy function
  const executeQuickBuy = async () => {
    if (!authenticated) {
      showSwapErrorToast('Please connect your wallet first');
      return;
    }

    if (!amount || parseFloat(amount) <= 0) {
      showSwapErrorToast('Please enter a valid amount in QuickBuy');
      return;
    }

    if (!token?.exchange_name || !['PumpSwap', 'PumpFun', 'LaunchLab'].includes(token.exchange_name)) {
      showSwapErrorToast('Unsupported exchange');
      return;
    }

    const walletInfo = await getSolanaWalletInfo();
    if (!walletInfo) {
      showSwapErrorToast('No Solana wallet found. Please ensure you have a Solana wallet connected.');
      return;
    }

    // Validate SOL balance
    try {
      const balanceRequest: BalanceRequest = {
        walletAddress: walletInfo.address
      };

      const balanceResponse = await getTokenBalance(balanceRequest);
      if (balanceResponse?.success) {
        const solBalance = parseFloat(balanceResponse.data.solBalance);
        const requestedAmount = parseFloat(amount);

        if (solBalance < requestedAmount) {
          const shortfall = requestedAmount - solBalance;
          showSwapErrorToast(
            `Insufficient SOL balance. You have ${solBalance.toFixed(4)} SOL, ` +
            `but trying to spend ${requestedAmount} SOL. Shortfall: ${shortfall.toFixed(4)} SOL.`
          );
          return;
        }
      }
    } catch (error) {
      console.error('Error checking balance:', error);
      showSwapErrorToast('Failed to check wallet balance');
      return;
    }

    setIsExecutingBuy(true);

    try {
      // Use default preset settings for quick buy
      const defaultPresetSettings = {
        slippage: 10,
        priority: 0.0001,
        bribe: 0.00005,
        mevMode: 'Off'
      };

      const swapRequest: SwapRequest = {
        tokenAddress: (token.address || token.id) as string,
        poolAddress: token.pool_address || '',
        dexType: token.exchange_name.toLowerCase(),
        amount: parseFloat(amount),
        direction: 'buy',
        slippage: defaultPresetSettings.slippage / 100,
        walletAddress: walletInfo.address,
        walletId: walletInfo.id,
        mevProtection: defaultPresetSettings.mevMode !== 'Off',
        bribeAmount: defaultPresetSettings.bribe,
        priorityLevel: 'high',
        priorityFee: defaultPresetSettings.priority
      };

      const result = await getSwapForExchange(token.exchange_name, swapRequest);

      if (result.success) {
        const transactionSignature = result.data.signature || result.data.transactionHash || '';
        const tokenSymbol = token.symbol || 'TOKEN';

        if (transactionSignature) {
          showSwapSuccessToast(
            'buy',
            tokenSymbol,
            transactionSignature,
            result.data.solscanUrl
          );
        } else {
          showSwapInfoToast(`Successfully bought ${tokenSymbol}`);
        }

        // Trigger balance refresh after successful transaction
        setTimeout(() => {
          console.log('Triggering balance refresh after successful quick buy');
          // Dispatch custom event to notify QuickBuy component to refresh balance
          window.dispatchEvent(new CustomEvent('quickBuySuccess', {
            detail: {
              tokenSymbol,
              transactionSignature,
              timestamp: Date.now()
            }
          }));
        }, 2000); // 2 second delay to allow blockchain confirmation

      } else {
        const errorMessage = result.error || 'Swap failed for unknown reason';
        showSwapErrorToast(errorMessage);
      }

    } catch (error) {
      console.error('Quick buy execution error:', error);
      showSwapErrorToast(error instanceof Error ? error.message : 'Unknown error occurred');
    } finally {
      setIsExecutingBuy(false);
    }
  };

  const handleZapClick = (e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent row click
    executeQuickBuy();
  };
  const formatNumber = (num: number | null | undefined): string => {
    if (num === null || num === undefined || isNaN(num)) return "--";
    if (num >= 1e12) return "$" + (num / 1e12).toFixed(2) + "T"; 
    if (num >= 1e9) return "$" + (num / 1e9).toFixed(2) + "B";   
    if (num >= 1e6) return "$" + (num / 1e6).toFixed(2) + "M";   
    if (num >= 1e3) return "$" + (num / 1e3).toFixed(2) + "K";   
    return num.toLocaleString(); 
  };
  const formatAddress = (address: string | undefined | null) => {
    if (!address || typeof address !== 'string') return 'N/A';
    return `${address.slice(0, 3)}...${address.slice(-4)}`;
  };
  const buyPercent = Math.round((stats.buys / (stats.buys + stats.sells)) * 100) || 0;
  
  // Function to toggle between circle and rectangle mode
  const handleCopy = (e: React.MouseEvent) => {
    e.stopPropagation();
    navigator.clipboard.writeText(address);
  
    toast(
      <div className="flex items-center gap-3">
        <Clipboard className="text-indigo-400 w-5 h-5" />
        <span>Address copied to clipboard</span>
      </div>,
      {
        autoClose: 2000, // disappears after 2 seconds
        closeButton: true,
        hideProgressBar: true,
        className: 'bg-[#1f1f1f] text-white font-medium border border-gray-700 rounded-lg shadow-md',
        icon: false,
      }
    );
  };
  
  const StatPill = ({ icon: Icon, value, color }: {
    icon?: any,
    value: string,
    color: 'green' | 'yellow' | 'red' | 'blue' | 'purple'
  }) => {
    const colorClasses = {
      green: "bg-emerald-500/10 text-emerald-400 border-emerald-500/20",
      yellow: "bg-yellow-500/10 text-yellow-400 border-yellow-500/20",
      red: "bg-red-500/10 text-red-400 border-red-500/20",
      blue: "bg-blue-500/10 text-blue-400 border-blue-500/20",
      purple: "bg-purple-500/10 text-purple-400 border-purple-500/20"
    };

    return (
      <div className={`px-2 py-1.5 rounded-lg border backdrop-blur-sm ${colorClasses[color]} flex items-center justify-center gap-1`}>
        {Icon && <Icon size={12} />}
        <span className="font-medium text-xs">{value}</span>
      </div>
    );
  };

  const zapBoxStyles = clsx(
    "absolute bg-[#1F1F1F] border border-gray-600 flex items-center gap-1 px-2 py-1 rounded-full text-lg",
    {
      "right-2 bottom-2": quickBuySize === "small",
      "right-3 bottom-3 scale-110": quickBuySize === "large",
      "left-auto right-4 bottom-4 rounded-md px-4 py-2 text-lg": quickBuySize === "mega",
    }
  );
  const [isHovered, setIsHovered] = useState(false);
  return (
    <div 
    onClick={(e) => handleRowClick(e, token)}
      className="relative group bg-[#181C20] hover:bg-[#141416] p-4  shadow-xl border border-slate-700/50 hover:border-slate-600/50 text-white w-full transition-all duration-300 cursor-pointer backdrop-blur-sm"
    >
      {/* Enhanced Zap hover box - now clickable */}
      <div
        onClick={handleZapClick}
        className={`${zapBoxStyles} opacity-0 group-hover:opacity-100 font-semibold text-sm transition-all duration-300 cursor-pointer hover:scale-105 transform group-hover:translate-y-1 ${
          isExecutingBuy
            ? "bg-yellow-600/90 text-white border-yellow-500/50"
            : settings.greyButtons
            ? "bg-slate-700/90 text-slate-300 border-slate-600/50 hover:bg-slate-600/90"
            : "bg-blue-600/90 text-white border-blue-500/50 hover:bg-blue-500/90"
        } ${isExecutingBuy ? 'cursor-wait' : ''}`}
        title={isExecutingBuy ? "Executing Buy..." : "Quick Buy"}
      >
        {isExecutingBuy ? (
          <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-white"></div>
        ) : (
          <Zap
            size={14}
            fill={settings.greyButtons ? '#CBD5E1' : 'white'}
            className={settings.greyButtons ? 'text-slate-300' : 'text-white'}
          />
        )}
        <span>
          {isExecutingBuy ? 'Buying...' : (
            <>
              {amount}
              {amount !== '' && ' SOL'}
            </>
          )}
        </span>
      </div>

      <div className="flex items-start gap-4">
        {/* Enhanced Avatar Section */}
        <div className="relative">
          <div className="relative w-20 h-20">
            {/* Enhanced Bonding Visualization */}
            {!settings.progressBar && (
              settings.circleImages ? (
                // Circular bonding ring with glow effect
                <div className="absolute inset-0 rounded-full pointer-events-none z-10">
                  {/* Background ring */}
                  <div
                    className="absolute inset-0 rounded-full opacity-60"
                    style={{
                      background: 'conic-gradient(from 0deg, #1e293b 0deg, #334155 360deg)',
                      maskImage: 'radial-gradient(circle, transparent 65%, black 67%, black 96%, transparent 100%)',
                      WebkitMaskImage: 'radial-gradient(circle, transparent 65%, black 67%, black 96%, transparent 100%)',
                    }}
                  />
                  {/* Progress ring with glow */}
                  <div
                    className="absolute inset-0 rounded-full"
                    style={{
                      background: `conic-gradient(
                        transparent 0deg 135deg,
                        #10b981 135deg ${135 + (bonding / 100) * 360}deg,
                        transparent ${135 + (bonding / 100) * 360}deg 360deg
                      )`,
                      maskImage: 'radial-gradient(circle, transparent 65%, black 67%, black 96%, transparent 100%)',
                      WebkitMaskImage: 'radial-gradient(circle, transparent 65%, black 67%, black 96%, transparent 100%)',
                      filter: 'drop-shadow(0 0 4px rgba(16, 185, 129, 0.4))'
                    }}
                  />
                </div>
              ) : (
                // Enhanced rectangular bonding border
                <>
                  {/* Static background borders */}
                  <div className="absolute bottom-0 right-0 h-0.5 bg-slate-600/50 rounded-lg w-full z-10" />
                  <div className="absolute bottom-0 left-0 w-0.5 bg-slate-600/50 rounded-lg h-full z-10" />
                  <div className="absolute top-0 left-0 h-0.5 bg-slate-600/50 rounded-lg w-full z-10" />
                  <div className="absolute top-0 right-0 w-0.5 bg-slate-600/50 rounded-lg h-full z-10" />

                  {/* Progressive green border with glow */}
                  <div
                    className="absolute bottom-0 right-0 h-0.5 bg-emerald-400 rounded-lg z-10 shadow-[0_0_4px_rgba(16,185,129,0.6)]"
                    style={{ width: `${Math.min(bonding, 25) * 4}%` }}
                  />
                  {bonding > 25 && (
                    <div
                      className="absolute bottom-0 left-0 w-0.5 bg-emerald-400 rounded-lg z-10 shadow-[0_0_4px_rgba(16,185,129,0.6)]"
                      style={{ height: `${Math.min(bonding - 25, 25) * 4}%` }}
                    />
                  )}
                  {bonding > 50 && (
                    <div
                      className="absolute top-0 left-0 h-0.5 bg-emerald-400 rounded-lg z-10 shadow-[0_0_4px_rgba(16,185,129,0.6)]"
                      style={{ width: `${Math.min(bonding - 50, 25) * 4}%` }}
                    />
                  )}
                  {bonding > 75 && (
                    <div
                      className="absolute top-0 right-0 w-0.5 bg-emerald-400 rounded-lg z-10 shadow-[0_0_4px_rgba(16,185,129,0.6)]"
                      style={{ height: `${Math.min(bonding - 75, 25) * 4}%` }}
                    />
                  )}
                </>
              )
            )}

            {/* Enhanced Image Container */}
            <div className={`relative w-full h-full ${settings.circleImages ? 'rounded-full' : 'rounded-lg'} overflow-hidden shadow-lg`}>
              {imageError || !imageUrl ? (
                <div 
                  className="w-full h-full flex items-center justify-center text-white text-2xl font-bold"
                  style={{ background: getBackgroundColor() }}
                >
                  {getTokenInitial()}
                </div>
              ) : (
                <img
                  src={imageUrl}
                  alt="Token"
                  className="object-cover w-full h-full transition-transform duration-300 group-hover:scale-105"
                  onLoad={handleImageLoad}
                  onError={handleImageError}
                />
              )}
              
              {/* Enhanced overlay image */}
              <div className="absolute -bottom-1 -right-1 z-20">
                <div className="relative">
                  {exchange_logo ? (
                    <img
                      src={exchange_logo}
                      alt="Exchange"
                      className="w-6 h-6 rounded-full border-2 border-white bg-white shadow-lg"
                    />
                  ) : (
                    <div className="w-6 h-6 rounded-full border-2 border-white bg-gray-700 shadow-lg flex items-center justify-center">
                      <span className="text-xs text-white">E</span>
                    </div>
                  )}
                  <div className="absolute inset-0 rounded-full bg-gradient-to-br from-transparent to-black/10"></div>
                </div>
              </div>
            </div>

            {/* Enhanced Progress Bar */}
            {settings.progressBar && (
              <div className="absolute -bottom-3 left-0 w-full h-1 bg-slate-700/50 rounded-full overflow-hidden z-10 shadow-inner">
                <div
                  className="h-full bg-gradient-to-r from-emerald-500 to-emerald-400 rounded-full transition-all duration-500 shadow-[0_0_8px_rgba(16,185,129,0.4)]"
                  style={{ width: `${bonding}%` }}
                />
              </div>
            )}
          </div>

          {/* Enhanced Address */}
          <div
      className="relative mt-2 w-16 font-mono py-2 rounded text-[10px] text-[#BBBBBB] cursor-pointer hover:text-[#6683FF] transition-colors"
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      onClick={handleCopy}
    >
      {formatAddress(address)}

      {/* Tooltip */}
      {isHovered && (
        <div className="absolute left-full ml-2 top-1/2 -translate-y-1/2 bg-[#1E1E2F] text-white text-[10px] px-2 py-1 rounded shadow-lg z-10 whitespace-nowrap">
          Copy - {address}
        </div>
      )}
    </div>
        </div>

        {/* Enhanced Project Info */}
        <div className="flex-1 min-w-0">
        <div className="flex items-center gap-1 mb-1">
          <h2 className="text-md font-bold text-white">{token.name}</h2>
          <span className="text-[#BBBBBB] text-sm font-medium px-1 py-0.5 rounded">
            {token.symbol}
          </span>
          <Copy
  onClick={handleCopy}
  className="w-4 h-4 cursor-pointer text-gray-500 hover:text-[#6683FF] transition-colors"
/>

        </div>
          {/* Enhanced Age and Socials */}
          <div className="flex items-center text-base text-emerald-400 mb-2 gap-3">
            <div className="flex items-center gap-1 ">
              {/* <Activity size={12} /> */}
              <TokenAge createdAt={createdAt} className="font-medium text-base" />
            </div>
            {customRows.socials && (
              <div className="flex items-center gap-2 text-slate-400">
                <Twitter size={18} className="hover:text-blue-400 transition-colors cursor-pointer" />
                <Globe size={18} className="hover:text-emerald-400 transition-colors cursor-pointer" />
                <Send size={18 } className="hover:text-purple-400 transition-colors cursor-pointer" />
              </div>
            )}
          </div>

          {/* Enhanced Stats Row */}
          <div className="flex text-base w-1/2 text-slate-400 gap-4 mb-2">
            {customRows.proTraders && (
              <div className="flex items-center gap-1">
                <TrendingUp size={12} className="text-blue-400" />
                <span>PT:</span>
                <span className="text-white font-medium">{stats.proTraders}</span>
              </div>
            )}
            {customRows.holders && (
              <div className="flex items-center gap-1">
                <Users size={12} className="text-purple-400" />
                <span>H:</span>
                <span className="text-white font-medium">{stats.holders}</span>
              </div>
            )}
            {customRows.devMigrations && (
              <div className="flex items-center gap-1">
                <Settings size={12} className="text-orange-400" />
                <span>DM:</span>
                <span className="text-white font-medium">{bonding}</span>
              </div>
            )}
          </div>
        </div>

        {/* Enhanced Market Info */}
        <div className="text-right text-base text-slate-300 space-y-1 ">
          {customRows.marketCap && (
            <div className={`flex items-center justify-end gap-1 ${metricsSize === "large" ? "text-lg" : "text-base"}`}>
              <span className="text-slate-400">MC</span>
              <span className="text-yellow-400 ">{formatNumber(marketCap)}</span>
            </div>
          )}
          {customRows.volume && (
            <div className="flex items-center justify-end gap-1">

              <span className="text-slate-400">V</span>
              <span className="text-white ">{formatNumber(volume)}</span>
            </div>
          )}

        </div>
      </div>

      {/* Enhanced Bottom Stat Pills */}
      {/* <div className="grid grid-cols-5 gap-2 mt-1 w-1/2">
        <StatPill icon={Shield} value={stats.top10Holders} color="green" />
        <StatPill icon={User} value={stats.devHolding} color="blue" />
        <StatPill icon={Target} value={stats.snipers} color="red" />
        <StatPill icon={AlertTriangle} value={stats.insiders} color="yellow" />
        <StatPill icon={Package} value={stats.bundlers} color="purple" />
      </div> */}
    </div>

        );
};


const SkeletonCard = () => (
  <div className=" p-3 border border-gray-600 rounded-lg  m-2">
    <div className="flex items-start">
      <div className="w-24 h-24  rounded-full mr-4"></div>
      <div className="flex-1 space-y-2">
        <div className="h-4 bg-gray-700 rounded w-1/2"></div>
        <div className="h-3 bg-gray-700 rounded w-1/3"></div>
        <div className="h-3 bg-gray-700 rounded w-2/3"></div>
        <div className="h-3 bg-gray-700 rounded w-1/4"></div>
      </div>
    </div>
    <div className="mt-3 grid grid-cols-5 gap-2">
      {Array.from({ length: 5 }).map((_, i) => (
        <div key={i} className="h-4 bg-gray-700 rounded-full"></div>
      ))}
    </div>
  </div>
);
const CategoryColumn = ({
  title,
  cards,
  customRows,
  settings,
  metricsSize,
  quickBuySize,
  amount,
  loading,
}: any) => {
  return (
    <div className="w-1/3 min-w-0 border-r border-gray-500 bg-[#181C20] flex flex-col max-h-screen">
      {/* Sticky Header */}
      <div className="sticky top-0 bg-[#181C20] z-10 flex items-center justify-between p-4 border-b border-gray-500">
        <h2 className="font-bold text-white">{title}</h2>
        <button className="text-gray-400 hover:text-white">
          <SlidersHorizontal size={18} />
        </button>
      </div>
  
      {/* Scrollable Content */}
      <div className="flex-1 min-h-0 overflow-y-auto">
        {loading
          ? Array.from({ length: 4 }).map((_, i) => <SkeletonCard key={i} />)
          : cards.map((card: CryptoCardProps, index: number) => (
              <CryptoCard
                key={index}
                {...card}
                token={card}
                amount={amount}
                quickBuySize={quickBuySize}
                customRows={customRows}
                settings={settings}
                metricsSize={metricsSize}
              />
            ))}
      </div>
    </div>
  );
  
  
};


interface PulseDashProps {
  settings: any;
  customRows: any;
  metricsSize: string;
  quickBuySize: string;
  amount: string;
  preset: number;
  setPreset: (value: number) => void;
  onPresetClick: () => void;
}

const PulseDash: React.FC<PulseDashProps> = ({
  settings,
  customRows,
  metricsSize,
  quickBuySize,
  amount,
  preset,
  setPreset,
  onPresetClick
}) => {
  const [newProjects, setNewProjects] = useState<CryptoCardProps[]>([]);
  const [bondingProjects, setBondingProjects] = useState<CryptoCardProps[]>([]);
  const [bondedProjects, setBondedProjects] = useState<CryptoCardProps[]>([]);

  // Use WebSocket hook for real-time pulse data

 
  console.log('🚀 [DEBUG] Pulse component about to call useWebSocketPulseData');

  // Add a visible test to verify React is running
  useEffect(() => {
    console.log('🚀 [DEBUG] Pulse component mounted - React is working!');
    // Test if we can make a simple fetch request
    fetch('/api/websocket/health')
      .then(response => response.json())
      .then(data => {
        console.log('🚀 [DEBUG] Health check from Pulse component:', data);
      })
      .catch(error => {
        console.error('🚀 [DEBUG] Health check failed from Pulse component:', error);
      });
  }, []);

  const {
    pulseData,
    isConnecting
  } = useWebSocketPulseData(true); // Enable WebSocket
  console.log('🚀 [DEBUG] useWebSocketPulseData returned:', { pulseData, isConnecting });
console.log("Pulse Data:", pulseData);
  const loading = isConnecting && !pulseData;

  const transformProjectToCard = (project: any) => ({
    imageUrl: project.image || '',
    name: project.name || '',
    symbol: project.symbol || '',
    age: project.age || '', // Keep for backward compatibility
    createdAt: project.createdAt || null, // Add createdAt field for age calculation
    marketCap: project.market_cap || '',
    volume: project.total_volume || '',
    txCount: project.txCount || '',
    price_change_24h:project.price_change_24h||0,
    pool_address:project.pool_address||'',
    price:project.current_price||'',
    address: project.id || '',
    bonding: project.bonding || project.bonding_percent || 0,
    supply:project.supply ||0,
    liquidity:project.liquidity ||0,
    exchange_name:project.exchange_name ||'',
    exchange_logo:project.exchange_logo ||'',
    network: project.network || '', // ✅ Added missing network field
    stats: {
      holders: project.stats?.holders || '',
      buys: project.stats?.buys || 0,
      sells: project.stats?.sells || 0,
      proTraders: project.stats?.proTraders || '',
      devMigrations: project.stats?.devMigrations || '',
      top10Holders: project.stats?.top10Holders || '',
      devHolding: project.stats?.devHolding || '',
      snipers: project.stats?.snipers || '',
      insiders: project.stats?.insiders || '',
      bundlers: project.stats?.bundlers || '',
      dexPaid: project.stats?.dexPaid || '',
    },
    socials: {
      telegram: project.socials?.telegram || '',
      website: project.socials?.website || ''
    },
    amount: amount || '',
    quickBuySize,
    customRows,
    settings,
    metricsSize,
    token: project // Add the token property
  });

  // Store previous pulse data for comparison
  const prevPulseDataRef = useRef<any>(null);

  // Update projects when pulse data changes
  useEffect(() => {
    if (pulseData) {
      // Check if data has actually changed by comparing with previous data
      const currentDataString = JSON.stringify(pulseData);
      const prevDataString = JSON.stringify(prevPulseDataRef.current);

      if (currentDataString !== prevDataString) {
        console.log('📡 Processing new pulse data (data changed):', {
          hasData: !!pulseData,
          newCount: pulseData.new?.length || 0,
          bondingCount: pulseData.bonding?.length || 0,
          bondedCount: pulseData.bonded?.length || 0
        });

        // Transform and deduplicate the data to prevent duplicate tokens
        const deduplicateTokens = (tokens: any[]) => {
          const seen = new Map();
          return tokens.filter(token => {
            const key = token.address || token.id || token.symbol;
            if (seen.has(key)) {
              return false;
            }
            seen.set(key, true);
            return true;
          });
        };

        const newTokens = deduplicateTokens(pulseData.new || []).map(transformProjectToCard);
        const bondingTokens = deduplicateTokens(pulseData.bonding || []).map(transformProjectToCard);
        const bondedTokens = deduplicateTokens(pulseData.bonded || []).map(transformProjectToCard);

        setNewProjects(newTokens);
        setBondingProjects(bondingTokens);
        setBondedProjects(bondedTokens);

        console.log('📊 Deduplicated tokens:', {
          newCount: newTokens.length,
          bondingCount: bondingTokens.length,
          bondedCount: bondedTokens.length
        });

        // Update the reference
        prevPulseDataRef.current = pulseData;
      } else {
        console.log('📊 Received identical pulse data, skipping update');
      }
    }
  }, [pulseData]);
  console.log("Rendered New Projects:", newProjects);
  console.log("Rendered Bonding Projects:", bondingProjects);
  console.log("Rendered Bonded Projects:", bondedProjects);
  
  const [showDropdown, setShowDropdown] = useState(false);
  // const [metricsSize, setMetricsSize] = useState('small');
  // const [quickBuySize, setQuickBuySize] = useState('small');

  // // Display settings toggles
  // const [settings, setSettings] = useState({
  //   greyButtons: true,
  //   // showSearchBar: true,
  
  //   circleImages: false,
  //   progressBar: true,
  //   compactTables: true,
  //   // metricsSize: metricsSize // Add metricsSize to settings
  // });

  

  // const [customRows, setCustomRows] = useState({
  //   marketCap: true,
  //   volume: true,
  //   tx: true,
  //   socials: true,
  //   holders: false,
  //   proTraders: false,
  //   devMigrations: false
  // });
  // const [amount, setAmount] = useState(""); // Entered amount
  const [modalOpen, setModalOpen] = useState(false);
  // const [preset, setPreset] = useState(3);

  const handlePresetClick = () => setModalOpen(true);
  // const handleAmountChange = (value: string) => {
  //   setAmount(value);
  //   console.log("Amount entered:", value); // Or send to backend, etc.
  // };

  return (
    <div className="flex flex-col min-h-screen bg-[#141416] text-gray-100">
      {/* DEBUG: Visual indicator that React is working */}
      <div style={{
        position: 'fixed',
        top: '10px',
        right: '10px',
        background: 'red',
        color: 'white',
        padding: '10px',
        zIndex: 9999,
        border: '2px solid yellow',
        fontSize: '14px'
      }}>
        🚀 REACT IS WORKING!<br/>
        WebSocket: {isConnecting ? 'Connecting...' : pulseData ? 'Connected' : 'Disconnected'}<br/>
        Data: {pulseData ? `${pulseData.new?.length || 0} new` : 'None'}
      </div>

      <div className="flex-grow px-4 pb-4">


        {/* Top section with Display and QuickBuy */}
        {/* <div className="flex justify-end gap-4">
          <div className="">
            <Display
              metricsSize={metricsSize}
              setMetricsSize={setMetricsSize}
              showDropdown={showDropdown}
              setShowDropdown={setShowDropdown}
              quickBuySize={quickBuySize}
              setQuickBuySize={setQuickBuySize}
              settings={settings}
              setSettings={setSettings}
              customRows={customRows}
              setCustomRows={setCustomRows}
            />
          </div>

          <div className="">
            <QuickBuy
              onAmountChange={handleAmountChange}
              selectedPreset={preset}
              onPresetClick={handlePresetClick}
            />
            {modalOpen && (
              <TradingSettingsModal
                visible={modalOpen}
                onClose={() => setModalOpen(false)}
                selectedPreset={preset}
                onPresetChange={setPreset}
              />
            )}
          </div>
        </div> */}

        {/* Middle section with CategoryColumns */}
        <div className={`flex overflow-hidden w-full h-full ${settings.compactTables ? 'gap-0' : 'gap-4'} mt-4`}>
          
        <CategoryColumn
  title="New"
  cards={newProjects}
  customRows={customRows}
  settings={settings}
  loading={loading}
  metricsSize={metricsSize}
  quickBuySize={quickBuySize}
  amount={amount}
/>

<CategoryColumn
  title="Bonding"
  cards={bondingProjects}
  customRows={customRows}
  settings={settings}
  loading={loading}
  metricsSize={metricsSize}
  quickBuySize={quickBuySize}
  amount={amount}
/>

<CategoryColumn
  title="Bonded"
  cards={bondedProjects}
  customRows={customRows}
  settings={settings}
  loading={loading}
  metricsSize={metricsSize}
  quickBuySize={quickBuySize}
  amount={amount}
/>

</div>





      </div>
      
      {/* Footer positioned at the bottom */}
      <Footer preset={preset} setPreset={setPreset} onPresetClick={handlePresetClick} />
      
    </div>
  );

};

export default PulseDash;
