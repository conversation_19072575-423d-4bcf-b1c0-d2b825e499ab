import React, { useState, useEffect } from "react";
import axios from "axios";
import { homeAPI } from "@/utils/api";

// Define interface for token data
interface TokenData {
  id: string;
  symbol?: string;
  name?: string;
  image?: string;
  current_price?: number;
  market_cap?: number;
  total_volume?: number;
  price_change_percentage_24h?: number;
  network?: string;
  baseimage?:string;
  liquidity?: number;
  address?: string; // Ensure address is part of the type
}

const MOBULA_API_KEY = "fffa68cd-6bde-4ac5-909d-eb627d8baca0"; // Replace with your key
const MOBULA_API_URL = "https://api.mobula.io/api/1/market/data";

// Add type for the prop
const SelectedTokensBar = ({ onActiveTokenChange }: { onActiveTokenChange: (token: TokenData | null) => void }) => {
  // Type the state
  const [storedTokenInfo, setStoredTokenInfo] = useState<TokenData[]>(() => {
    try {
      // Fix localStorage parsing
      const stored = localStorage.getItem("selectedTokens");
      return stored ? JSON.parse(stored) : [];
    } catch (e) {
      console.error("Error parsing stored tokens:", e);
      return [];
    }
  });

  useEffect(() => {
    const interval = setInterval(() => {
      const stored = localStorage.getItem("selectedTokens");
      const newStored: TokenData[] = stored ? JSON.parse(stored) : [];

      // More robust comparison: Check length and token IDs
      let hasChanged = false;
      if (newStored.length !== storedTokenInfo.length) {
        hasChanged = true;
      } else {
        // Use a temporary copy of storedTokenInfo for comparison inside interval
        const currentInfo = [...storedTokenInfo]; 
        const currentIds = currentInfo.map(t => t.id).sort().join(',');
        const newIds = newStored.map(t => t.id).sort().join(',');
        if (currentIds !== newIds) {
          hasChanged = true;
        }
      }

      if (hasChanged) {
        console.log("SelectedTokensBar: Detected change in localStorage, updating storedTokenInfo.");
        setStoredTokenInfo(newStored);
      }
    }, 1000); 
  
    return () => clearInterval(interval);
  }, []); // Keep dependency array empty
  
  // Type the state
  const [selectedTokens, setSelectedTokens] = useState<TokenData[]>([]); 
  // Type the state
  const [activeToken, setActiveToken] = useState<TokenData | null>(null);
  const [loading, setLoading] = useState<boolean>(true);

  // Memoize token info to prevent unnecessary renders
  const memoizedTokenInfo = React.useMemo(() => {
    return JSON.stringify(storedTokenInfo);
  }, [storedTokenInfo]);
  
  useEffect(() => {
    let isMounted = true;
    let controller: AbortController | null = null;
    
    const fetchTokenDetails = async () => {
      if (storedTokenInfo.length === 0) {
        if (isMounted) setSelectedTokens([]);
        setLoading(false);
        return;
      }

      try {
        // Create an abort controller for this effect instance
        controller = new AbortController();
        const signal = controller.signal;
        
        setLoading(true);
        const tokensWithDetails: TokenData[] = [];
        
        // Use Promise.all to fetch all tokens in parallel instead of sequentially
        const fetchPromises = storedTokenInfo.map(async (token) => {
          const { id, network } = token;
          if (!id || !network) return null;

          try {
            // Add a small delay between requests to avoid overwhelming the server
            const response = await homeAPI.fetchMarketInfo('market-data', {
              address: token.id,
              network: token.network,
              signal // Pass the abort signal to allow cleanup
            });

            if (signal.aborted) return null;

            const tokenData = response.data?.data;
            if (!tokenData) return null;

            return {
              id,
              symbol: tokenData.symbol,
              name: tokenData.name,
              baseimage:tokenData.native["logo"],
              image: tokenData.logo || undefined,
              current_price: tokenData.price,
              market_cap: tokenData.market_cap,
              total_volume: tokenData.volume,
              price_change_percentage_24h: tokenData.price_change_24h,
              network,
              liquidity: tokenData.liquidity,
              address: token.id
            };
          } catch (err) {
            if (!signal.aborted) { // Only log if not aborted
              console.error(`Error fetching token ${id} on ${network}:`, err);
            }
            return null;
          }
        });
        
        // Wait for all promises to settle and filter out null results
        const results = await Promise.all(fetchPromises);
        const validResults = results.filter(result => result !== null) as TokenData[];

        if (isMounted && !signal.aborted) {
          setSelectedTokens(validResults);
          setLoading(false);
        }
      } catch (err) {
        if (isMounted) {
          console.error("Failed to fetch token details:", err);
          setLoading(false);
        }
      }
    };

    // Throttle the fetching to prevent too many calls
    const timeoutId = setTimeout(() => {
      fetchTokenDetails();
    }, 300);

    // Cleanup function
    return () => { 
      isMounted = false;
      if (controller) controller.abort();
      clearTimeout(timeoutId);
    };
  }, [memoizedTokenInfo]); // Use the memoized version to prevent unnecessary re-renders // Re-fetch if stored info changes

  useEffect(() => {
    if (selectedTokens.length === 0) return;

    const storedActiveTokenString = localStorage.getItem("activeToken");
    let storedActiveToken: TokenData | null = null;
    if (storedActiveTokenString) {
      try {
        storedActiveToken = JSON.parse(storedActiveTokenString) as TokenData;
      } catch (e) {
        console.error("Error parsing active token:", e);
      }
    }

    if (storedActiveToken) {
      const existingToken = selectedTokens.find(t => t.id === storedActiveToken?.id && t.network === storedActiveToken?.network);

      if (existingToken) {
        const completeExistingToken = {
          ...existingToken,
          address: existingToken.address || existingToken.id
        };
        setActiveToken(completeExistingToken);
        onActiveTokenChange(completeExistingToken);
        return;
      }
    }

    const firstToken = selectedTokens[0];
    const completeFirstToken = {
      ...firstToken,
      address: firstToken.address || firstToken.id
    };
    setActiveToken(completeFirstToken);
    onActiveTokenChange(completeFirstToken);
    localStorage.setItem("activeToken", JSON.stringify(completeFirstToken));

  }, [selectedTokens, onActiveTokenChange]);

  const handleTokenClick = (token: TokenData) => {
    const completeToken = {
      ...token,
      address: token.address || token.id 
    };
    
    if (activeToken?.id === completeToken.id && activeToken?.network === completeToken.network) return;

    setActiveToken(completeToken);
    localStorage.setItem("activeToken", JSON.stringify(completeToken));
    onActiveTokenChange(completeToken);
  };

  const removeToken = (e: React.MouseEvent, id: string) => {
    e.stopPropagation();

    const updatedTokenInfo = storedTokenInfo.filter(token => token.id !== id);
    const updatedTokens = selectedTokens.filter(token => token.id !== id);

    setStoredTokenInfo(updatedTokenInfo);
    setSelectedTokens(updatedTokens);
    localStorage.setItem("selectedTokens", JSON.stringify(updatedTokenInfo));

    if (activeToken?.id === id) {
      if (updatedTokens.length > 0) {
        const newActiveToken = updatedTokens[updatedTokens.length - 1];
        const completeNewActiveToken = {
          ...newActiveToken,
          address: newActiveToken.address || newActiveToken.id
        };
        setActiveToken(completeNewActiveToken);
        localStorage.setItem("activeToken", JSON.stringify(completeNewActiveToken));
        onActiveTokenChange(completeNewActiveToken);
      } else {
        setActiveToken(null);
        localStorage.removeItem("activeToken");
        onActiveTokenChange(null);
      }
    }
  };

  return (
    <div className="w-full overflow-x-auto">
      <div className="flex items-center gap-4 bg-[#141416] p-2 rounded-xl min-w-max md:min-w-0">
        <div className="p-2 bg-[#181C20] rounded-full shrink-0">
          <svg width="40" height="40" viewBox="0 0 40 40" fill="none">
            <rect width="40" height="40" rx="20" fill="#181C20" />
          </svg>
        </div>

        {loading ? (
          Array.from({ length: 3 }).map((_, i) => (
            <div key={i} className="w-28 h-10 bg-[#1e1e1e] animate-pulse rounded-full" />
          ))
        ) : (
          selectedTokens.map((token) => (
            <div
              key={`${token.id}-${token.network}`}
              onClick={() => handleTokenClick(token)}
              className={`flex items-center gap-2 px-4 py-3 rounded-full cursor-pointer transition-all whitespace-nowrap shrink-0 ${
                activeToken?.id === token.id && activeToken?.network === token.network
                  ? (token.price_change_percentage_24h ?? 0) > 0
                    ? "bg-[#214638]"
                    : "bg-[#311D27]"
                  : "bg-[#181C20]"
              }`}
            >
              <span className="text-white text-lg">{token.symbol?.toUpperCase() || ""}</span>
              <span className={`text-sm ${(token.price_change_percentage_24h ?? 0) > 0 ? "text-green-400" : "text-[#FF329B]"}`}>
                {(token.price_change_percentage_24h ?? 0) > 0 ? '▲' : '▼'} {Math.abs(token.price_change_percentage_24h ?? 0).toFixed(2)}%
              </span>
              {selectedTokens.length > 1 && (
                <button
                  onClick={(e) => removeToken(e, token.id)}
                  className="text-gray-400 hover:text-white"
                >
                  X
                </button>
              )}
            </div>
          ))
        )}
      </div>
    </div>
  );
};

export default SelectedTokensBar;