import React, { useState, useEffect } from 'react';

interface TokenData {
  id: string;
  symbol: string;
  name: string;
  price: number;
  marketCap: number;
  change24h: number;
  volume24h: number;
}

interface MarketCapListProps {
  isVisible: boolean;
  onToggle: () => void;
}

// Mock data for demonstration - in real app, this would come from API
const mockTokens: TokenData[] = [
  {
    id: '1',
    symbol: 'BALA',
    name: 'Balancer',
    price: 4.52,
    marketCap: 45200000,
    change24h: -0.19,
    volume24h: 7970000
  },
  {
    id: '2',
    symbol: 'SOL',
    name: 'Solana',
    price: 245.67,
    marketCap: 115000000000,
    change24h: 2.45,
    volume24h: 2500000000
  },
  {
    id: '3',
    symbol: 'USDC',
    name: 'USD Coin',
    price: 1.00,
    marketCap: 32000000000,
    change24h: 0.01,
    volume24h: 5200000000
  },
  {
    id: '4',
    symbol: 'RAY',
    name: 'Raydium',
    price: 5.23,
    marketCap: 1200000000,
    change24h: 5.67,
    volume24h: 89000000
  },
  {
    id: '5',
    symbol: 'ORCA',
    name: '<PERSON><PERSON>',
    price: 3.45,
    marketCap: 890000000,
    change24h: -1.23,
    volume24h: 45000000
  }
];

const MarketCapList: React.FC<MarketCapListProps> = ({ isVisible, onToggle }) => {
  const [tokens, setTokens] = useState<TokenData[]>(mockTokens);
  const [sortBy, setSortBy] = useState<'marketCap' | 'price' | 'change24h'>('marketCap');

  const formatNumber = (num: number): string => {
    if (num >= 1e9) {
      return `$${(num / 1e9).toFixed(2)}B`;
    }
    if (num >= 1e6) {
      return `$${(num / 1e6).toFixed(2)}M`;
    }
    if (num >= 1e3) {
      return `$${(num / 1e3).toFixed(2)}K`;
    }
    return `$${num.toFixed(2)}`;
  };

  const formatPrice = (price: number): string => {
    if (price < 0.01) {
      return `$${price.toFixed(6)}`;
    }
    return `$${price.toFixed(2)}`;
  };

  const sortedTokens = [...tokens].sort((a, b) => {
    switch (sortBy) {
      case 'marketCap':
        return b.marketCap - a.marketCap;
      case 'price':
        return b.price - a.price;
      case 'change24h':
        return b.change24h - a.change24h;
      default:
        return 0;
    }
  });

  if (!isVisible) {
    return (
      <button
        onClick={onToggle}
        className="fixed right-4 top-1/2 transform -translate-y-1/2 bg-gray-800 text-white p-2 rounded-l-lg border border-gray-700 hover:bg-gray-700 transition-colors z-10"
        title="Show Market Cap List"
      >
        ◀
      </button>
    );
  }

  return (
    <div className="w-80 bg-black border-l border-gray-800 flex flex-col">
      {/* Header */}
      <div className="p-4 border-b border-gray-800">
        <div className="flex items-center justify-between">
          <h3 className="text-white font-semibold">Market Cap</h3>
          <button
            onClick={onToggle}
            className="text-gray-400 hover:text-white transition-colors"
            title="Hide Market Cap List"
          >
            ▶
          </button>
        </div>
        
        {/* Sort Options */}
        <div className="flex space-x-2 mt-3">
          {[
            { key: 'marketCap', label: 'Cap' },
            { key: 'price', label: 'Price' },
            { key: 'change24h', label: '24h' }
          ].map((option) => (
            <button
              key={option.key}
              onClick={() => setSortBy(option.key as any)}
              className={`px-2 py-1 text-xs rounded transition-colors ${
                sortBy === option.key
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
              }`}
            >
              {option.label}
            </button>
          ))}
        </div>
      </div>

      {/* Token List */}
      <div className="flex-1 overflow-y-auto">
        {sortedTokens.map((token, index) => (
          <div
            key={token.id}
            className="p-3 border-b border-gray-900 hover:bg-gray-900 transition-colors cursor-pointer"
          >
            <div className="flex items-center justify-between">
              <div className="flex-1">
                <div className="flex items-center space-x-2">
                  <span className="text-xs text-gray-500">#{index + 1}</span>
                  <span className="text-white font-medium">{token.symbol}</span>
                </div>
                <div className="text-xs text-gray-400 mt-1">{token.name}</div>
              </div>
              
              <div className="text-right">
                <div className="text-white font-mono text-sm">
                  {formatPrice(token.price)}
                </div>
                <div className="text-xs text-gray-400">
                  {formatNumber(token.marketCap)}
                </div>
                <div className={`text-xs font-mono ${
                  token.change24h >= 0 ? 'text-green-400' : 'text-red-400'
                }`}>
                  {token.change24h >= 0 ? '+' : ''}{token.change24h.toFixed(2)}%
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Footer */}
      <div className="p-3 border-t border-gray-800 text-center">
        <button className="text-xs text-gray-400 hover:text-white transition-colors">
          View All Markets
        </button>
      </div>
    </div>
  );
};

export default MarketCapList;
