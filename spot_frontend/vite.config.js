import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import path from 'path';
import { nodePolyfills } from 'vite-plugin-node-polyfills';

export default defineConfig({
  plugins: [
    react({
      // Enable JSX processing for .js and .jsx files
      include: '**/*.{jsx,js,tsx,ts}',
    }),
    nodePolyfills()
  ],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, 'src'),
    },
    extensions: ['.mjs', '.js', '.jsx', '.ts', '.tsx', '.json']
  },
  optimizeDeps: {
    esbuildOptions: {
      loader: {
        '.js': 'jsx',
        '.ts': 'tsx',
      },
      define: {
        global: 'globalThis'
      }
    },
  },
  server: {
    port: 4001,
    strictPort: true, // Fail if port is already in use instead of trying another port
    open: true,
    host: true, // Listen on all addresses
    allowedHosts: ['redfyn.lrbinfotech.com', 'redfyn.crypfi.io','localhost', '*************'],
    proxy: {
      // Order matters! More specific paths must come first
      '/solana-service': {
        target: process.env.NODE_ENV === 'production' ? 'http://*************:6001' : 'http://localhost:6001',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/solana-service/, '/api'),
        configure: (proxy, _options) => {
          proxy.on('proxyReq', (proxyReq, req, _res) => {
            console.log('Solana proxy request:', req.url, '-> target:', proxyReq.path);
          });
        }
      },

      '/liquidity-api': {
        target: process.env.NODE_ENV === 'production' ? 'http://*************:3047' : 'http://localhost:3047',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/liquidity-api/, '/api')
      },

      '/api': {
        target: process.env.NODE_ENV === 'production' ? 'http://*************:5001' : 'http://localhost:5001',
        changeOrigin: true,
        configure: (proxy, _options) => {
          proxy.on('error', (err, _req, _res) => {
            console.log('proxy error', err);
          });
          proxy.on('proxyReq', (proxyReq, req, _res) => {
            // Add CORS headers
            proxyReq.setHeader('Origin', req.headers.host);
          });
        }
      }
    },
  },
  build: {
    rollupOptions: {
      external: [
        'rpc-websockets/dist/lib/client',
        'rpc-websockets/dist/lib/client/websocket.browser'
      ]
    }
  }
}); 